--- ######################### ---
--- ## DISCORD.GG/CODEWAVE ## ---
--- ######################### ---


Config = {}
Config.Framework = 'ESX' -- 'ESX' or 'QBCore' (case sensitive)
Config.EsExtendedRename = "es_extended" -- Only change this if you've renamed es_extended resource name.


Config.TimeToCraft = 8000
Config.RequiredJob = 'none' -- CHANGE TO YOUR OWN JOB (### IF YOU DO NOT WANT TO USE JOB, USE 'none')


Config.NPC = {
    Model = "a_m_m_soucent_03", -- Change to your desired NPC model
    Coords = vector3(-1230.9382, -796.3212, 16.2040), -- Change to your desired NPC spawn location (THE LAST VALUE IS THE Z VALUE, YOU MAY NEED TO DECRASE BY 1)
    Heading = 305.0, -- Adjust the heading as needed
    BlipSprite = 58,        -- Example blip icon, see https://wiki.rage.mp/index.php?title=Blips for blip IDs
    BlipScale = 0.5,
    BlipColour = 67,
    BlipName = "IceBox"
}

Config.VanStuff = {
    interactionLocation = vector3(-1266.0505, -816.5042, 16.0991), -- Example coordinates for interaction
    vanModel = 'codewave_icebox', -- Model of the van to spawn
    depositAmount = 1500, --deposit required to take van out

    vanSpawnLocation = {
        coords = vector3(-1224.3247, -801.7002, 16.9381), -- Van spawn coordinates
        heading = 214.0 -- Heading of the van when spawned
    },
    toolboxModels = {
        'prop_tool_box_01', -- Model of the first toolbox
        'prop_tool_box_02'  -- Model of the second toolbox
    }
}


-- ### BE SURE TO CHANGE THE "DRAWABLE ID" THIS IS WHAT SLOT YOUR CHAIN WILL BE ON
--- So this is quite simple for example "vvs_nolife" is a item which is in your inventory, that item is usable & will attach slot 7 on chains.
Config.Accessories = {
    vvs_nolife = {componentId = 7, drawableId = 271, textureId = 0},
    tiger_gold = {componentId = 7, drawableId = 274, textureId = 0},
    vvs_gold = {componentId = 7, drawableId = 269, textureId = 0},
    ace_chain = {componentId = 7, drawableId = 267, textureId = 0},
    silver_chain = {componentId = 7, drawableId = 265, textureId = 0},
    gold_chain = {componentId = 7, drawableId = 263, textureId = 0},
    rose_gold_chain = {componentId = 7, drawableId = 261, textureId = 0},
    vvs_silver_chain = {componentId = 7, drawableId = 257, textureId = 0},
    watch_1 = {componentId = 6, drawableId = 1, textureId = 0}, -- Example for watches, This is supported but you have to figure out drawable id's ect
    -- Add more items as needed
}

-- Default drawable ID when the accessory is removed
Config.DefaultDrawableIds = {
    [6] = 15, -- Default for watches
    [7] = 15, -- Default for chains
}


-- Add anything you want here (For example if you want a new required item you can add it here for them to purchase!)
Config.Items = {
    { name = "Silver Bar", id = "silver_bar", img = "image/silver_bar.png", price = 100 }, -- You don't really need too touch anything apart from PRICE
    { name = "Gold Bar", id = "gold_bar", img = "image/gold_bar.png", price = 200 },
    { name = "Loose diamonds", id = "loose_diamonds", img = "image/loose_diamonds.png", price = 200 },
    { name = "Diamond adhesive", id = "diamond_adhesive", img = "image/diamond_adhesive.png", price = 300 },
    { name = "Icebox Work Bench", id = "icebox_workbench", img = "image/icebox_workbench.png", price = 100 },
    { name = "Icebox client phone", id = "icebox_phone", img = "image/icebox_phone.png", price = 300 }
}



--- Too add your own change, Copy the template below, (IF YOU NEED HELP, MAKE A TICKET VIA DISCORD.GG/CODEWAVE)
Config.CraftingRecipes = {
    { name = "VVS No Life Chain", id = "vvs_nolife", img = "image/vvs_nolife.png", requiredItems = {  { id = "silver_bar", quantity = 1 }, { id = "loose_diamonds", quantity = 2 }, { id = "diamond_adhesive", quantity = 1 } } },
    { name = "Tiger Diamond Gold Chain", id = "tiger_gold", img = "image/tiger_gold.png", requiredItems = {  { id = "silver_bar", quantity = 1 }, { id = "loose_diamonds", quantity = 3 }, { id = "diamond_adhesive", quantity = 1 } } },
    { name = "VVS Gold Chain", id = "vvs_gold", img = "image/vvs_gold.png", requiredItems = {  { id = "silver_bar", quantity = 1 }, { id = "loose_diamonds", quantity = 3 }, { id = "diamond_adhesive", quantity = 1 } } },
    { name = "VVS Ace Chain", id = "ace_chain", img = "image/ace_chain.png", requiredItems = {  { id = "silver_bar", quantity = 1 }, { id = "loose_diamonds", quantity = 2 }, { id = "diamond_adhesive", quantity = 2 } } },
    { name = "Silver Chain", id = "silver_chain", img = "image/silver_chain.png", requiredItems = {  { id = "silver_bar", quantity = 1 }, { id = "loose_diamonds", quantity = 3 }, { id = "diamond_adhesive", quantity = 1 } } },
    { name = "Gold Chain", id = "gold_chain", img = "image/gold_chain.png", requiredItems = {  { id = "silver_bar", quantity = 1 }, { id = "loose_diamonds", quantity = 3 }, { id = "diamond_adhesive", quantity = 1 } } },
    { name = "Rose Gold Chain", id = "rose_gold_chain", img = "image/rose_gold_chain.png", requiredItems = {  { id = "silver_bar", quantity = 1 }, { id = "loose_diamonds", quantity = 3 }, { id = "diamond_adhesive", quantity = 1 } } },
    { name = "VVS Silver Chain", id = "vvs_silver_chain", img = "image/vvs_silver_chain.png", requiredItems = {  { id = "silver_bar", quantity = 1 }, { id = "loose_diamonds", quantity = 3 }, { id = "diamond_adhesive", quantity = 1 } } },
    -- Add more chains if you want
}

Config.PlayMusicWhileUIIsOpen = false --- True = music plays, False = music doesn't. (YOU CAN CHANGE MUSIC IN SOUNDS FILE)
Config.MusicVolume = 0.05 --- Music volume in the menu, Only applies if above is set to True


Config.iceboxTableItem = 'icebox_workbench' --- The table item
Config.PropItemName = 'prop_table_03' -- You can change this if you want (to basically any prop, make it make sense though!)


Config.SellMaxItem = 12 -- Defines max amount of items sold, eg it will now take the whole 12 if possible, if not it will take a lower amount.


Config.RequiredItems = {
    { item = 'vvs_nolife', priceMin = 1000, priceMax = 3500 },
    { item = 'tiger_gold', priceMin = 1000, priceMax = 3500 },
    { item = 'vvs_gold', priceMin = 1000, priceMax = 3500 },
    { item = 'ace_chain', priceMin = 1000, priceMax = 3500 },
    { item = 'silver_chain', priceMin = 1000, priceMax = 3500 },
    { item = 'gold_chain', priceMin = 1000, priceMax = 3500 },
    { item = 'rose_gold_chain', priceMin = 1000, priceMax = 3500 },
    { item = 'vvs_silver_chain', priceMin = 1000, priceMax = 3500 }
}


--- ### DISCORD.GG/CODEWAVE ### ---