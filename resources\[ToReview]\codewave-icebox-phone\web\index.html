<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iPhone UI</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <audio id="click-sound" src="sounds/click.mp3" preload="auto"></audio>
    <audio id="notification-sound" src="sounds/notification.mp3" preload="auto"></audio> <!-- Notification sound -->
    <div class="iphone" id="iphone">
        <div class="notch-container">
            <div class="dynamic-island">
                <div class="camera"></div>
                <div class="speaker"></div>
            </div>
        </div>
        <div class="screen">
            <div class="time-battery-container">
                <div class="time">12:00</div>
                <div class="status-right">
                    <div class="battery">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="wifi"></div>
                </div>
            </div>
            <div class="apps">
                <div class="app" id="deliver-products">
                    <i class="fas fa-shipping-fast"></i>
                    <span>Deliver Products</span>
                </div>
                <div class="app" id="settings-app">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </div>
            </div>
            <div class="title-box">
                <span>Work Phone</span>
            </div>
        </div>
    </div>

    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="modal-title"></h2>
            <p id="modal-description"></p>
            <div id="settings-content" style="display: none;">
                <label for="border-color-picker">Select Border Color:</label>
                <input type="color" id="border-color-picker" name="border-color-picker" value="#ff69b4">
                <button id="apply-color-button">Apply Color</button>
            </div>
            <button id="start-button">Start</button>
        </div>
    </div>

    <!-- Custom notification UI -->
    <div id="notification" class="notification">
        <div class="notification-header">
            <div class="notification-icon">
                <img src="image/image.png" alt="Icon">
            </div>
            <div class="notification-title">
                <strong>Messages</strong>
                <span class="notification-time">now</span>
            </div>
        </div>
        <div class="notification-message">
            <p id="notification-message">Notification</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
