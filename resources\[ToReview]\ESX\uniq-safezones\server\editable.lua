-- Discord Logs
local webhook = Shared.Logs.webhook

function logger(message)
    if Shared.Logs.enabled then
        if webhook ~= '' then
            PerformHttpRequest(webhook, function(status) end, 'POST', json.encode({
                username = cache.resource,
                embeds = {{
                    color = Shared.Logs.color,
                    title = Shared.Logs.servername,
                    description = message,
                    footer = {
                        text = os.date(),
                        icon_url = Shared.Logs.icon_url
                    }
                }}
            }), { ['Content-Type'] = 'application/json' })
        end
    end
end

-- Command
lib.addCommand(Shared.Commands.safezonecreator.name, {
    help = L('command.creator.description'),
    restricted = Shared.Commands.safezonecreator.restricted
}, function(source)
    TriggerClientEvent("safezonecreator:adminMenu", source)
end)