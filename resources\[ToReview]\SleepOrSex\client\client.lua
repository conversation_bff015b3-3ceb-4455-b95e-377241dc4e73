ESX                       = exports['es_extended']:getSharedObject()
local PlayerData              = {}
local PlayerLoaded = false
local sleeper = 0
local bedOpen = false
pregnant = false
pregnant2 = false
local condom = false
local levonorgestrel = false
local IsNear = false

Citizen.CreateThread(function()
  --[[while not NetworkIsSessionStarted() do 
    Wait(250)
  end]]
  while ESX == nil do
    Citizen.Wait(100)
  end
  while ESX.GetPlayerData().job == nil do -- Wait for character (job) to load (support for kashacters, etc)
        Wait(250)
  end
  PlayerLoaded = true
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
  	PlayerData = xPlayer
	PlayerLoaded = true
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	PlayerData.job = job
end)

AddEventHandler('playerSpawned', function()
	pregnant = false
	ESX.TriggerServerCallback('podol:gethamil', function(pregnant)
		if pregnant then
			while not PlayerLoaded do
				Citizen.Wait(1000)
			end
			TriggerEvent('podol:resetpregnantStatus')
		end
	end)
end)

RegisterNetEvent('podol:resetpregnantStatus')
AddEventHandler('podol:resetpregnantStatus', function()
	Citizen.Wait(10000)
	pregnant = true
	ESX.ShowNotification(_U('stillpregnant'))
end)

RegisterNetEvent('podol:updatepregnantStatus')
AddEventHandler('podol:updatepregnantStatus', function()
	pregnant = true
	pregnant2 = true
	Citizen.Wait(10000)
	TriggerEvent('skinchanger:getSkin', function(skin)
		if skin.sex == 1 then
			RequestAnimDict("clothingtrousers")
				while not HasAnimDictLoaded("clothingtrousers") do
					Citizen.Wait(0)
				end
			TaskPlayAnim(PlayerPedId(), "clothingtrousers", "try_trousers_neutral_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
			--TriggerEvent('podol:addpregnantStatus')
			TriggerEvent('skinchanger:loadClothes', skin, Config.PregnantOutfit.female)
			ESX.ShowNotification(_U('pregnant'))
		end
	end)
end)

local isInVehicle, isSwimming, isUnderwater = false, false, false
Citizen.CreateThread(function()  

  while true do

    local secondsBetweenAnimPregnant = Config.AnimMillSecPregnant
    local playerPed = GetPlayerPed(-1)

	  Citizen.Wait(secondsBetweenAnimPregnant)

    if pregnant and not isSwimming and not isUnderwater and not isInVehicle then

	  RequestAnimDict("misscarsteal4@actor")
      while not HasAnimDictLoaded("misscarsteal4@actor") do
      Citizen.Wait(100)
      end

      TaskPlayAnim(GetPlayerPed(-1), "misscarsteal4@actor", "stumble", 1.0, 4.0, 5000, 0, 0, false, false, false)
      Citizen.Wait(5000)

      ClearPedSecondaryTask(GetPlayerPed(-1))
    end
  end
end)

Citizen.CreateThread(function()
    while true do
	local lPed = GetPlayerPed(-1)
	isSwimming = IsPedSwimming(lPed)
	isUnderwater = IsPedSwimmingUnderWater(lPed)
	isInVehicle = IsPedInAnyVehicle(lPed, true)
    Citizen.Wait(500)
    end
end)

function addpregnantStatus()
	TriggerServerEvent('podol:sethamil', true)
	TriggerServerEvent("podol:updatePregnant")
	pregnant = true
end

function removepregnantStatus()
	TriggerServerEvent('podol:sethamil', false)
	TriggerServerEvent("podol:removePregnant")
	pregnant = false
end

RegisterNetEvent('podol:addpregnantStatus')
AddEventHandler('podol:addpregnantStatus', function()
	addpregnantStatus()
end)

RegisterNetEvent('podol:removepregnantStatus')
AddEventHandler('podol:removepregnantStatus', function()
	removepregnantStatus()
end)

if not Config.ItemViagra then
	RegisterCommand(Config.Command, function(source, args, raw)
	local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
	if closestPlayer ~= -1 and closestDistance <= 3.0 then
		TriggerServerEvent('podol:starteweyan', GetPlayerServerId(closestPlayer))
	else
		ESX.ShowNotification(_U('noplayernearby'))
	end
	end, false)
end

RegisterNetEvent('podol:viagrasync')
AddEventHandler('podol:viagrasync', function(target)
	local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
	if closestPlayer ~= -1 and closestDistance <= 3.0 then
		local ped = GetPlayerPed(-1)
		RequestAnimDict("mp_suicide")
		while not HasAnimDictLoaded("mp_suicide") do
		Citizen.Wait(100)
		end

		TaskPlayAnim(ped, "mp_suicide", "pill_fp", 8.0, 8.0, -1, 50, 0, false, false, false)

		Citizen.Wait(3000)
		ClearPedSecondaryTask(GetPlayerPed(-1))
		TriggerEvent('esx_basicneeds:onDrink')
		TriggerServerEvent('podol:starteweyan', GetPlayerServerId(closestPlayer))
	else
		TriggerServerEvent('podol:refundviagra')
		ESX.ShowNotification(_U('noplayernearby'))
	end
end)

RegisterNetEvent('podol:condomsync')
AddEventHandler('podol:condomsync', function(target)
	local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
	if closestPlayer ~= -1 and closestDistance <= 3.0 then
		TriggerServerEvent('podol:condomuse', GetPlayerServerId(closestPlayer))
	else
		TriggerServerEvent('podol:refundcondom')
		ESX.ShowNotification(_U('noplayernearby'))
	end
end)

RegisterNetEvent('podol:testpacksync')
AddEventHandler('podol:testpacksync', function(target)
	local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
	if closestPlayer ~= -1 and closestDistance <= 3.0 then
		TriggerServerEvent('podol:testpackuse', GetPlayerServerId(closestPlayer))
	else
		TriggerServerEvent('podol:refundtestpack')
		ESX.ShowNotification(_U('noplayernearby'))
	end
end)

RegisterNetEvent('podol:testpack')
AddEventHandler('podol:testpack', function()
	local ped = GetPlayerPed(-1)
	RequestAnimDict("anim@amb@clubhouse@tutorial@bkr_tut_ig3@")
        while not HasAnimDictLoaded("anim@amb@clubhouse@tutorial@bkr_tut_ig3@") do
            Citizen.Wait(0)
        end
    TaskPlayAnim(ped, "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", "machinic_loop_mechandplayer", 1.0, 1.0, 2000, 0, 0, false, false, false)
	Citizen.Wait(2000)
	ClearPedSecondaryTask(GetPlayerPed(-1))
	ESX.ShowNotification(_U('testpack'))
end)

RegisterNetEvent('podol:testpack2')
AddEventHandler('podol:testpack2', function()
	local ped = GetPlayerPed(-1)
	RequestAnimDict("missbigscore1switch_trevor_piss")
        while not HasAnimDictLoaded("missbigscore1switch_trevor_piss") do
            Citizen.Wait(0)
        end
    TaskPlayAnim(ped, "missbigscore1switch_trevor_piss", "piss_outro", 1.0, 1.0, 2000, 0, 0, false, false, false)
	Citizen.Wait(2000)
	ClearPedSecondaryTask(GetPlayerPed(-1))
	TriggerServerEvent('podol:checkPregnant')
	ESX.ShowNotification(_U('testpack2'))
end)

RegisterNetEvent('podol:condom')
AddEventHandler('podol:condom', function()
	local ped = GetPlayerPed(-1)
	RequestAnimDict("anim@amb@clubhouse@tutorial@bkr_tut_ig3@")
        while not HasAnimDictLoaded("anim@amb@clubhouse@tutorial@bkr_tut_ig3@") do
            Citizen.Wait(0)
        end
    TaskPlayAnim(ped, "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", "machinic_loop_mechandplayer", 1.0, 1.0, 2000, 0, 0, false, false, false)
	Citizen.Wait(2000)
	ClearPedSecondaryTask(GetPlayerPed(-1))
	ESX.ShowNotification(_U('condom'))
	condom = true
	Citizen.Wait(Config.Condom)
	condom = false
end)

RegisterNetEvent('podol:condom2')
AddEventHandler('podol:condom2', function()
	local ped = GetPlayerPed(-1)
	RequestAnimDict("missbigscore1switch_trevor_piss")
        while not HasAnimDictLoaded("missbigscore1switch_trevor_piss") do
            Citizen.Wait(0)
        end
    TaskPlayAnim(ped, "missbigscore1switch_trevor_piss", "piss_outro", 1.0, 1.0, 2000, 0, 0, false, false, false)
	Citizen.Wait(2000)
	ClearPedSecondaryTask(GetPlayerPed(-1))
	ESX.ShowNotification(_U('condom2'))
	condom = true
	Citizen.Wait(Config.Condom)
	condom = false
end)

RegisterNetEvent('podol:levonorgestrel')
AddEventHandler('podol:levonorgestrel', function()
	RequestAnimDict("mp_suicide")
  while not HasAnimDictLoaded("mp_suicide") do
  Citizen.Wait(100)
  end

  TaskPlayAnim(GetPlayerPed(-1), "mp_suicide", "pill_fp", 8.0, 8.0, -1, 50, 0, false, false, false)

  Citizen.Wait(3000)
  ClearPedSecondaryTask(GetPlayerPed(-1))
	levonorgestrel = true
	Citizen.Wait(Config.Levonorgestrel)
	levonorgestrel = false
end)

RegisterNetEvent('podol:abortion')
AddEventHandler('podol:abortion', function()
	RequestAnimDict("mp_suicide")
  while not HasAnimDictLoaded("mp_suicide") do
  Citizen.Wait(100)
  end

  TaskPlayAnim(GetPlayerPed(-1), "mp_suicide", "pill_fp", 8.0, 8.0, -1, 50, 0, false, false, false)

  Citizen.Wait(3000)
  ClearPedSecondaryTask(GetPlayerPed(-1))
	Citizen.Wait(Config.Abortion)
	RequestAnimDict("oddjobs@taxi@tie")
    while not HasAnimDictLoaded("oddjobs@taxi@tie") do
    Citizen.Wait(100)
    end

    TaskPlayAnim(GetPlayerPed(-1), "oddjobs@taxi@tie", "vomit_outside", 8.0, 8.0, -1, 50, 0, false, false, false)
    Citizen.Wait(7000)
	pregnant = false
	pregnant2 = false
	TriggerEvent('podol:removepregnantStatus')
	ClearPedSecondaryTask(GetPlayerPed(-1))
	ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin)
		TriggerEvent('skinchanger:loadSkin', skin)
	end)
end)

local bed_models = Config.Bed

Citizen.CreateThread(function()
	while true do
		--Citizen.Wait(1)
		sleeper = 1000
		local letSleep = true
		local ped = GetPlayerPed(PlayerId())
		local playerPed = GetPlayerPed(-1)
		local player = GetPlayerPed(-1)
		local pedPos = GetEntityCoords(ped, false)
		for a = 1, #bed_models do
		  local bed = GetClosestObjectOfType(pedPos.x, pedPos.y, pedPos.z, 3.0, GetHashKey(bed_models[a]), false, 1, 1)
          if bed ~= 0 and not bedOpen then
		  local bedOffset = GetOffsetFromEntityInWorldCoords(bed, 0.0, 0.0, 0.0)
          local bedHeading = GetEntityHeading(bed)
          local distance = Vdist(pedPos.x, pedPos.y, pedPos.z, bedOffset.x, bedOffset.y, bedOffset.z)
			if distance <= 10 then
				letSleep = false
				sleeper = 250
			if distance <= 3 then
				letSleep = false
				sleeper = 5
				local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
				if closestPlayer ~= -1 and closestDistance <= 3.0 then
					DisplayHelpText(_U('havesex'))
					if IsControlJustPressed(1,38) and not pregnant2 then
						TriggerServerEvent('podol:starteweyankasur', GetPlayerServerId(closestPlayer))
					end
				else
					DisplayHelpText(_U('sleep'))
					if IsControlJustPressed(1,38) then
						TriggerEvent('podol:tidur')
					end
				end
				break
			end
			end
		else
			sleeper = 1000
		end
		end
		Citizen.Wait(sleeper)
		if letSleep then
		Citizen.Wait(5000)
		end
	end
end)

RegisterNetEvent('podol:gayaeweyan')
AddEventHandler('podol:gayaeweyan', function(target)
	local playerPed = GetPlayerPed(-1)
	local targetPed = GetPlayerPed(GetPlayerFromServerId(target))
	local pedPos = GetEntityCoords(playerPed, false)
	
	Citizen.Wait(5000)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	FreezeEntityPosition(playerPed, true)
	RequestAnimDict('misscarsteal2pimpsex')

	while not HasAnimDictLoaded('misscarsteal2pimpsex') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.0, 0.60, 0.0, 120.0, 0.0, 180.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'misscarsteal2pimpsex', 'pimpsex_hooker', 1.0, -1.0, -1, 1, 1, false, false, false)
	if Config.ItemViagra then
		Citizen.Wait(Config.BlowJobStand + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.BlowJobStand)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(GetPlayerPed(-1), true, false)
	
	RequestAnimDict('misscarsteal2pimpsex')

	while not HasAnimDictLoaded('misscarsteal2pimpsex') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.05, 0.35, -0.1, 120.0, 0.0, 180.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'misscarsteal2pimpsex', 'shagloop_hooker', 1.0, -1.0, -1, 1, 1, false, false, false)

	if Config.ItemViagra then
		Citizen.Wait(Config.SexStand + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.SexStand)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(GetPlayerPed(-1), true, false)
	
	RequestAnimDict('rcmpaparazzo_2')

	while not HasAnimDictLoaded('rcmpaparazzo_2') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.015, 0.25, 0.0, 0.9, 0.3, 0.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'rcmpaparazzo_2', 'shag_loop_poppy', 1.0, -1.0, -1, 1, 1, false, false, false)

	if Config.ItemViagra then
		Citizen.Wait(Config.Doggy + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.Doggy)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(playerPed, true, false)
	
	SetEntityCoords(playerPed, pedPos.x, pedPos.y, pedPos.z-1.8)
	
	RequestAnimDict('oddjobs@towing')

	while not HasAnimDictLoaded('oddjobs@towing') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.8, -0.1, -0.1, 0.0, 0.3, 0.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'oddjobs@towing', 'f_blow_job_loop', 1.0, -1.0, -1, 1, 1, false, false, false)
	
	if Config.ItemViagra then
		Citizen.Wait(Config.BlowJob + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.BlowJob)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(playerPed, true, false)
	
	RequestAnimDict('mini@prostitutes@sexlow_veh')

	while not HasAnimDictLoaded('mini@prostitutes@sexlow_veh') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.8, 0.0, 0.0, 0.0, 0.3, 0.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'mini@prostitutes@sexlow_veh', 'low_car_sex_loop_female', 1.0, -1.0, -1, 1, 1, false, false, false)
	
	if Config.ItemViagra then
		Citizen.Wait(Config.WoT + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.WoT)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(playerPed, true, false)
	
	RequestAnimDict('oddjobs@assassinate@vice@sex')

	while not HasAnimDictLoaded('oddjobs@assassinate@vice@sex') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.8, -0.1, 0.0, 0.0, 0.3, 0.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'oddjobs@assassinate@vice@sex', 'frontseat_carsex_loop_f', 1.0, -1.0, -1, 1, 1, false, false, false)
	
	if Config.ItemViagra then
		Citizen.Wait(Config.WoT2 + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.WoT2)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(playerPed, true, false)
	
	RequestAnimDict('random@drunk_driver_2')

	while not HasAnimDictLoaded('random@drunk_driver_2') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, -0.7, 0.0, -0.01, 0.0, 0.3, 0.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'random@drunk_driver_2', 'cardrunksex_loop_f', 1.0, -1.0, -1, 1, 1, false, false, false)

	if Config.ItemViagra then
		Citizen.Wait(Config.WoT3 + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.WoT3)
	end
	DetachEntity(playerPed, true, false)
	FreezeEntityPosition(playerPed, false)
	ClearPedTasks(playerPed)
	
	if Config.AllowPregnant then
		if togel == 1 and not condom and not levonorgestrel then
			TriggerEvent('skinchanger:getSkin', function(skin)
				if skin.sex == 1 then
					--Citizen.Wait(Config.Pregnant)
					TriggerEvent('podol:addpregnantStatus')
					--TriggerEvent('skinchanger:loadClothes', skin, Config.PregnantOutfit.female)
					ESX.ShowNotification(_U('indicationpregnant'))
				else
					ESX.ShowNotification(_U('cantpregnant'))
				end
			end)
		end
	end
end)

RegisterNetEvent('podol:gayaeweyan2')
AddEventHandler('podol:gayaeweyan2', function()
	local playerPed = GetPlayerPed(-1)
	local pedPos = GetEntityCoords(playerPed, false)
	
	Citizen.Wait(5000)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	FreezeEntityPosition(playerPed, true)
	RequestAnimDict('misscarsteal2pimpsex')

	while not HasAnimDictLoaded('misscarsteal2pimpsex') do
		Citizen.Wait(10)
	end

	TaskPlayAnim(playerPed, 'misscarsteal2pimpsex', 'pimpsex_punter', 1.0, -1.0, -1, 1, 1, false, false, false)

	if Config.ItemViagra then
		Citizen.Wait(Config.BlowJobStand + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.BlowJobStand)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	TaskPlayAnim(playerPed, 'misscarsteal2pimpsex', 'shagloop_pimp', 1.0, -1.0, -1, 1, 1, false, false, false)
	
	if Config.ItemViagra then
		Citizen.Wait(Config.SexStand + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.SexStand)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	RequestAnimDict('rcmpaparazzo_2')

	while not HasAnimDictLoaded('rcmpaparazzo_2') do
		Citizen.Wait(10)
	end
	
	TaskPlayAnim(playerPed, 'rcmpaparazzo_2', 'shag_loop_a',1.0, -1.0, -1, 1, 1, false, false, false)
	
	if Config.ItemViagra then
		Citizen.Wait(Config.Doggy + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.Doggy)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	SetEntityCoords(playerPed, pedPos.x, pedPos.y, pedPos.z-1.8)
	
	RequestAnimDict('oddjobs@towing')

	while not HasAnimDictLoaded('oddjobs@towing') do
		Citizen.Wait(10)
	end
	
	TaskPlayAnim(playerPed, 'oddjobs@towing', 'm_blow_job_loop',1.0, -1.0, -1, 1, 1, false, false, false)
	
	if Config.ItemViagra then
		Citizen.Wait(Config.BlowJob + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.BlowJob)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	RequestAnimDict('mini@prostitutes@sexlow_veh')

	while not HasAnimDictLoaded('mini@prostitutes@sexlow_veh') do
		Citizen.Wait(10)
	end
	
	TaskPlayAnim(playerPed, 'mini@prostitutes@sexlow_veh', 'low_car_sex_loop_player',1.0, -1.0, -1, 1, 1, false, false, false)
	
	if Config.ItemViagra then
		Citizen.Wait(Config.WoT + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.WoT)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	RequestAnimDict('oddjobs@assassinate@vice@sex')

	while not HasAnimDictLoaded('oddjobs@assassinate@vice@sex') do
		Citizen.Wait(10)
	end
	
	TaskPlayAnim(playerPed, 'oddjobs@assassinate@vice@sex', 'frontseat_carsex_loop_m',1.0, -1.0, -1, 1, 1, false, false, false)
	
	if Config.ItemViagra then
		Citizen.Wait(Config.WoT2 + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.WoT2)
	end
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	RequestAnimDict('random@drunk_driver_2')

	while not HasAnimDictLoaded('random@drunk_driver_2') do
		Citizen.Wait(10)
	end
	
	TaskPlayAnim(playerPed, 'random@drunk_driver_2', 'cardrunksex_loop_m',1.0, -1.0, -1, 1, 1, false, false, false)
	if Config.ItemViagra then
		Citizen.Wait(Config.WoT3 + Config.ViagraExtraTime)
	else
		Citizen.Wait(Config.WoT3)
	end
	FreezeEntityPosition(playerPed, false)
	ClearPedTasks(playerPed)

end)

RegisterNetEvent('podol:gayakasur')
AddEventHandler('podol:gayakasur', function(target)
	local playerPed = GetPlayerPed(-1)
	local targetPed = GetPlayerPed(GetPlayerFromServerId(target))
	local pedPos = GetEntityCoords(playerPed, false)
	for a = 1, #bed_models do
		  local bed = GetClosestObjectOfType(pedPos.x, pedPos.y, pedPos.z, 3.0, GetHashKey(bed_models[a]), false, 1, 1)
          if bed ~= 0 and not bedOpen then
		  local bedOffset = GetOffsetFromEntityInWorldCoords(bed, 0.0, -0.7, 0.0)
          local bedHeading = GetEntityHeading(bed)
          local distance = Vdist(pedPos.x, pedPos.y, pedPos.z, bedOffset.x, bedOffset.y, bedOffset.z)
		  local togel = math.random(1,Config.Chance)
	bedOpen = true
	if Config.RemoveClothes then
	RequestAnimDict("clothingtrousers")
	while not HasAnimDictLoaded("clothingtrousers") do
		Citizen.Wait(0)
	end
	TaskPlayAnim(PlayerPedId(), "clothingtrousers", "try_trousers_neutral_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
	TriggerEvent('skinchanger:getSkin', function(skin)
			if skin.sex == 0 then
				TriggerEvent('skinchanger:loadClothes', skin, Config.Naked.male)
			else
				TriggerEvent('skinchanger:loadClothes', skin, Config.Naked.female)
			end
		end)
	end
	
	Citizen.Wait(5000)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	FreezeEntityPosition(playerPed, true)

	RequestAnimDict('misscarsteal2pimpsex')

	while not HasAnimDictLoaded('misscarsteal2pimpsex') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.0, 0.60, 0.0, 120.0, 0.0, 180.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'misscarsteal2pimpsex', 'pimpsex_hooker', 1.0, -1.0, -1, 1, 1, false, false, false)

	Citizen.Wait(Config.BlowJobStand)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(GetPlayerPed(-1), true, false)
	
	RequestAnimDict('misscarsteal2pimpsex')

	while not HasAnimDictLoaded('misscarsteal2pimpsex') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.05, 0.35, -0.1, 120.0, 0.0, 180.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'misscarsteal2pimpsex', 'shagloop_hooker', 1.0, -1.0, -1, 1, 1, false, false, false)

	Citizen.Wait(Config.SexStand)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(GetPlayerPed(-1), true, false)
	
	RequestAnimDict('rcmpaparazzo_2')

	while not HasAnimDictLoaded('rcmpaparazzo_2') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.015, 0.25, 0.0, 0.9, 0.3, 0.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'rcmpaparazzo_2', 'shag_loop_poppy', 1.0, -1.0, -1, 1, 1, false, false, false)

	Citizen.Wait(Config.Doggy)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(playerPed, true, false)
	SetEntityHeading(playerPed, bedHeading-180)
	if bedOffset.z > 0 then
		SetEntityCoords(playerPed, bedOffset.x, bedOffset.y, bedOffset.z-0.2)
	else
		SetEntityCoords(playerPed, bedOffset.x, bedOffset.y, bedOffset.z+0.2)
	end
	RequestAnimDict('oddjobs@towing')

	while not HasAnimDictLoaded('oddjobs@towing') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.8, -0.1, -0.1, 0.0, 0.3, 0.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'oddjobs@towing', 'f_blow_job_loop', 1.0, -1.0, -1, 1, 1, false, false, false)
	
	Citizen.Wait(Config.BlowJob)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(playerPed, true, false)
	
	RequestAnimDict('mini@prostitutes@sexlow_veh')

	while not HasAnimDictLoaded('mini@prostitutes@sexlow_veh') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.8, 0.0, 0.0, 0.0, 0.3, 0.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'mini@prostitutes@sexlow_veh', 'low_car_sex_loop_female', 1.0, -1.0, -1, 1, 1, false, false, false)
	
	Citizen.Wait(Config.WoT)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(playerPed, true, false)
	
	RequestAnimDict('oddjobs@assassinate@vice@sex')

	while not HasAnimDictLoaded('oddjobs@assassinate@vice@sex') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, 0.8, -0.1, 0.0, 0.0, 0.3, 0.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'oddjobs@assassinate@vice@sex', 'frontseat_carsex_loop_f', 1.0, -1.0, -1, 1, 1, false, false, false)
	
	Citizen.Wait(Config.WoT2)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(playerPed, true, false)
	if bedOffset.z > 0 then
		SetEntityCoords(playerPed, bedOffset.x, bedOffset.y, bedOffset.z-0.3)
	else
		SetEntityCoords(playerPed, bedOffset.x, bedOffset.y, bedOffset.z+0.3)
	end
	RequestAnimDict('random@drunk_driver_2')

	while not HasAnimDictLoaded('random@drunk_driver_2') do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(playerPed, targetPed, 9816, -0.7, 0.0, 0.0, 0.0, 0.3, 0.0, 0, 0, 0, 0, 2, 1)
	TaskPlayAnim(playerPed, 'random@drunk_driver_2', 'cardrunksex_loop_f', 1.0, -1.0, -1, 1, 1, false, false, false)
	
	Citizen.Wait(Config.WoT3)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	DetachEntity(playerPed, true, false)
	
	FreezeEntityPosition(playerPed, false)
	RequestAnimDict("switch@franklin@bed")
	while not HasAnimDictLoaded("switch@franklin@bed") do
		Citizen.Wait(0)
	end
	TaskPlayAnim(PlayerPedId(), "switch@franklin@bed", "sleep_getup_rubeyes", 8.0, -8.0, -1, 0, 0.0, false, false, false)
	Citizen.Wait(9000)
	
	ClearPedTasks(playerPed)
						
	if Config.Sex then
	SetEntityHealth(playerPed, 200)
	end
	if Config.DecreaseHungerAndThirstAfterSex then
		TriggerServerEvent("podol:sleep")
	end
						
	if Config.UseClothes then
	RequestAnimDict("clothingtrousers")
	while not HasAnimDictLoaded("clothingtrousers") do
		Citizen.Wait(0)
	end
	TaskPlayAnim(PlayerPedId(), "clothingtrousers", "try_trousers_neutral_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
	if Config.AllowPregnant then
		if togel == 1 and not condom and not levonorgestrel then
			TriggerEvent('skinchanger:getSkin', function(skin)
			if skin.sex == 1 then
				--Citizen.Wait(Config.Pregnant)
				TriggerEvent('podol:addpregnantStatus')
				--TriggerEvent('skinchanger:loadClothes', skin, Config.PregnantOutfit.female)
				ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin)
					TriggerEvent('skinchanger:loadSkin', skin)
				end)
				ESX.ShowNotification(_U('indicationpregnant'))
			else
				ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin)
					TriggerEvent('skinchanger:loadSkin', skin)
				end)
				ESX.ShowNotification(_U('cantpregnant'))
					end
				end)
			else
				ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin)
					TriggerEvent('skinchanger:loadSkin', skin)
				end)
			end
		end
	end
						
	bedOpen = false
	break
		end
	end
end)

RegisterNetEvent('podol:gayakasur2')
AddEventHandler('podol:gayakasur2', function()
	local playerPed = GetPlayerPed(-1)
	local pedPos = GetEntityCoords(playerPed, false)
		for a = 1, #bed_models do
		  local bed = GetClosestObjectOfType(pedPos.x, pedPos.y, pedPos.z, 3.0, GetHashKey(bed_models[a]), false, 1, 1)
          if bed ~= 0 and not bedOpen then
		  local bedOffset = GetOffsetFromEntityInWorldCoords(bed, 0.0, -0.7, 0.0)
          local bedHeading = GetEntityHeading(bed)
          local distance = Vdist(pedPos.x, pedPos.y, pedPos.z, bedOffset.x, bedOffset.y, bedOffset.z)
	bedOpen = true
	if Config.RemoveClothes then
	RequestAnimDict("clothingtrousers")
	while not HasAnimDictLoaded("clothingtrousers") do
		Citizen.Wait(0)
	end
	TaskPlayAnim(PlayerPedId(), "clothingtrousers", "try_trousers_neutral_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
	TriggerEvent('skinchanger:getSkin', function(skin)
			if skin.sex == 0 then
				TriggerEvent('skinchanger:loadClothes', skin, Config.Naked.male)
			else
				TriggerEvent('skinchanger:loadClothes', skin, Config.Naked.female)
			end
		end)
	end
	
	Citizen.Wait(5000)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	FreezeEntityPosition(playerPed, true)

	RequestAnimDict('misscarsteal2pimpsex')

	while not HasAnimDictLoaded('misscarsteal2pimpsex') do
		Citizen.Wait(10)
	end

	TaskPlayAnim(playerPed, 'misscarsteal2pimpsex', 'pimpsex_punter', 1.0, -1.0, -1, 1, 1, false, false, false)

	Citizen.Wait(Config.BlowJobStand)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	TaskPlayAnim(playerPed, 'misscarsteal2pimpsex', 'shagloop_pimp', 1.0, -1.0, -1, 1, 1, false, false, false)
	
	Citizen.Wait(Config.SexStand)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	RequestAnimDict('rcmpaparazzo_2')

	while not HasAnimDictLoaded('rcmpaparazzo_2') do
		Citizen.Wait(10)
	end
	
	TaskPlayAnim(playerPed, 'rcmpaparazzo_2', 'shag_loop_a',1.0, -1.0, -1, 1, 1, false, false, false)
	
	Citizen.Wait(Config.Doggy)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	SetEntityHeading(playerPed, bedHeading-180)
	if bedOffset.z > 0 then
		SetEntityCoords(playerPed, bedOffset.x, bedOffset.y, bedOffset.z-0.2)
	else
		SetEntityCoords(playerPed, bedOffset.x, bedOffset.y, bedOffset.z+0.2)
	end
	RequestAnimDict('oddjobs@towing')

	while not HasAnimDictLoaded('oddjobs@towing') do
		Citizen.Wait(10)
	end
	
	TaskPlayAnim(playerPed, 'oddjobs@towing', 'm_blow_job_loop',1.0, -1.0, -1, 1, 1, false, false, false)
	
	Citizen.Wait(Config.BlowJob)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	RequestAnimDict('mini@prostitutes@sexlow_veh')

	while not HasAnimDictLoaded('mini@prostitutes@sexlow_veh') do
		Citizen.Wait(10)
	end
	
	TaskPlayAnim(playerPed, 'mini@prostitutes@sexlow_veh', 'low_car_sex_loop_player',1.0, -1.0, -1, 1, 1, false, false, false)
	
	Citizen.Wait(Config.WoT)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	
	RequestAnimDict('oddjobs@assassinate@vice@sex')

	while not HasAnimDictLoaded('oddjobs@assassinate@vice@sex') do
		Citizen.Wait(10)
	end
	
	TaskPlayAnim(playerPed, 'oddjobs@assassinate@vice@sex', 'frontseat_carsex_loop_m',1.0, -1.0, -1, 1, 1, false, false, false)
	
	Citizen.Wait(Config.WoT2)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	if bedOffset.z > 0 then
		SetEntityCoords(playerPed, bedOffset.x, bedOffset.y, bedOffset.z-0.3)
	else
		SetEntityCoords(playerPed, bedOffset.x, bedOffset.y, bedOffset.z+0.3)
	end
	RequestAnimDict('random@drunk_driver_2')

	while not HasAnimDictLoaded('random@drunk_driver_2') do
		Citizen.Wait(10)
	end
	
	TaskPlayAnim(playerPed, 'random@drunk_driver_2', 'cardrunksex_loop_m',1.0, -1.0, -1, 1, 1, false, false, false)
	Citizen.Wait(Config.WoT3)
	DoScreenFadeOut(5000)
	Citizen.Wait(5000)
	DoScreenFadeIn(5000)
	FreezeEntityPosition(playerPed, false)
	RequestAnimDict("switch@franklin@bed")
	while not HasAnimDictLoaded("switch@franklin@bed") do
		Citizen.Wait(0)
	end
	TaskPlayAnim(PlayerPedId(), "switch@franklin@bed", "sleep_getup_rubeyes", 8.0, -8.0, -1, 0, 0.0, false, false, false)
	Citizen.Wait(9000)
	
	ClearPedTasks(playerPed)
						
	if Config.Sex then
		SetEntityHealth(playerPed, 200)
	end
	if Config.DecreaseHungerAndThirstAfterSex then
		TriggerServerEvent("podol:sleep")
	end
						
	if Config.UseClothes then
	RequestAnimDict("clothingtrousers")
	while not HasAnimDictLoaded("clothingtrousers") do
		Citizen.Wait(0)
	end
	TaskPlayAnim(PlayerPedId(), "clothingtrousers", "try_trousers_neutral_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
	ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin)
		TriggerEvent('skinchanger:loadSkin', skin)
		end)
	end
						
	bedOpen = false
	break
		end
	end
end)

RegisterNetEvent('podol:tidur')
AddEventHandler('podol:tidur', function()
		local ped = GetPlayerPed(PlayerId())
		local playerPed = GetPlayerPed(-1)
		local player = GetPlayerPed(-1)
		local pedPos = GetEntityCoords(ped, false)
		for a = 1, #bed_models do
		  local bed = GetClosestObjectOfType(pedPos.x, pedPos.y, pedPos.z, 3.0, GetHashKey(bed_models[a]), false, 1, 1)
          if bed ~= 0 and not bedOpen then
		  local bedOffset = GetOffsetFromEntityInWorldCoords(bed, 0.0, -0.7, 0.0)
          local bedHeading = GetEntityHeading(bed)
          local distance = Vdist(pedPos.x, pedPos.y, pedPos.z, bedOffset.x, bedOffset.y, bedOffset.z)
					bedOpen = true
					if Config.UsePajama then
						RequestAnimDict("clothingtrousers")
						while not HasAnimDictLoaded("clothingtrousers") do
							Citizen.Wait(0)
						end
						TaskPlayAnim(PlayerPedId(), "clothingtrousers", "try_trousers_neutral_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
						TriggerEvent('skinchanger:getSkin', function(skin)
							if skin.sex == 0 then
								TriggerEvent('skinchanger:loadClothes', skin, Config.Pajama.male)
							else
								TriggerEvent('skinchanger:loadClothes', skin, Config.Pajama.female)
								end
							end)
					end
						Citizen.Wait(5000)
					if bedOffset.x >= 0 and bedOffset.y >= 0 and bedOffset.z >= 0 then
						if bedHeading >= 0 and bedHeading <= 9 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y+0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 9 and bedHeading <= 19 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 19 and bedHeading <= 29 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 29 and bedHeading <= 39 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 39 and bedHeading <= 49 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 49 and bedHeading <= 59 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 59 and bedHeading <= 69 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 69 and bedHeading <= 79 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 79 and bedHeading <= 89 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.75, bedOffset.y+0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 89 and bedHeading <= 99 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 99 and bedHeading <= 109 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 109 and bedHeading <= 119 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 119 and bedHeading <= 129 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 129 and bedHeading <= 139 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 139 and bedHeading <= 149 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 149 and bedHeading <= 159 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 159 and bedHeading <= 169 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 169 and bedHeading <= 179 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.05, bedOffset.y-0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 179 and bedHeading <= 189 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y-0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 189 and bedHeading <= 199 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 199 and bedHeading <= 209 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 209 and bedHeading <= 219 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 219 and bedHeading <= 229 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 229 and bedHeading <= 239 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 239 and bedHeading <= 249 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 249 and bedHeading <= 259 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 259 and bedHeading <= 269 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.75, bedOffset.y-0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 269 and bedHeading <= 279 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 279 and bedHeading <= 289 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 289 and bedHeading <= 299 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 299 and bedHeading <= 309 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 309 and bedHeading <= 319 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 319 and bedHeading <= 329 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 329 and bedHeading <= 339 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 339 and bedHeading <= 349 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 349 and bedHeading <= 360 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.05, bedOffset.y+0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
					end
					if bedOffset.x <= 0 and bedOffset.y >= 0 and bedOffset.z >= 0 then
						if bedHeading >= 0 and bedHeading <= 9 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y+0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 9 and bedHeading <= 19 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 19 and bedHeading <= 29 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 29 and bedHeading <= 39 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 39 and bedHeading <= 49 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 49 and bedHeading <= 59 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 59 and bedHeading <= 69 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 69 and bedHeading <= 79 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 79 and bedHeading <= 89 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.75, bedOffset.y+0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 89 and bedHeading <= 99 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 99 and bedHeading <= 109 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 109 and bedHeading <= 119 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 119 and bedHeading <= 129 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 129 and bedHeading <= 139 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 139 and bedHeading <= 149 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 149 and bedHeading <= 159 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 159 and bedHeading <= 169 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 169 and bedHeading <= 179 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.05, bedOffset.y-0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 179 and bedHeading <= 189 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y-0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 189 and bedHeading <= 199 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 199 and bedHeading <= 209 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 209 and bedHeading <= 219 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 219 and bedHeading <= 229 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 229 and bedHeading <= 239 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 239 and bedHeading <= 249 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 249 and bedHeading <= 259 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 259 and bedHeading <= 269 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.75, bedOffset.y-0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 269 and bedHeading <= 279 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 279 and bedHeading <= 289 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 289 and bedHeading <= 299 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 299 and bedHeading <= 309 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 309 and bedHeading <= 319 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 319 and bedHeading <= 329 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 329 and bedHeading <= 339 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 339 and bedHeading <= 349 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 349 and bedHeading <= 360 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.05, bedOffset.y+0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
					end
					if bedOffset.x >= 0 and bedOffset.y <= 0 and bedOffset.z >= 0 then
						if bedHeading >= 0 and bedHeading <= 9 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y+0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 9 and bedHeading <= 19 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 19 and bedHeading <= 29 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 29 and bedHeading <= 39 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 39 and bedHeading <= 49 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 49 and bedHeading <= 59 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 59 and bedHeading <= 69 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 69 and bedHeading <= 79 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 79 and bedHeading <= 89 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.75, bedOffset.y+0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 89 and bedHeading <= 99 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 99 and bedHeading <= 109 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 109 and bedHeading <= 119 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 119 and bedHeading <= 129 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 129 and bedHeading <= 139 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 139 and bedHeading <= 149 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 149 and bedHeading <= 159 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 159 and bedHeading <= 169 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 169 and bedHeading <= 179 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.05, bedOffset.y-0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 179 and bedHeading <= 189 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y-0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 189 and bedHeading <= 199 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 199 and bedHeading <= 209 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 209 and bedHeading <= 219 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 219 and bedHeading <= 229 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 229 and bedHeading <= 239 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 239 and bedHeading <= 249 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 249 and bedHeading <= 259 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 259 and bedHeading <= 269 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.75, bedOffset.y-0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 269 and bedHeading <= 279 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 279 and bedHeading <= 289 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 289 and bedHeading <= 299 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 299 and bedHeading <= 309 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 309 and bedHeading <= 319 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 319 and bedHeading <= 329 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 329 and bedHeading <= 339 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 339 and bedHeading <= 349 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 349 and bedHeading <= 360 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.05, bedOffset.y+0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
					end
					--end
					
					if bedOffset.x >= 0 and bedOffset.y >= 0 and bedOffset.z <= 0 then
						if bedHeading >= 0 and bedHeading <= 9 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y+0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 9 and bedHeading <= 19 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 19 and bedHeading <= 29 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 29 and bedHeading <= 39 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 39 and bedHeading <= 49 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 49 and bedHeading <= 59 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 59 and bedHeading <= 69 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 69 and bedHeading <= 79 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 79 and bedHeading <= 89 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.75, bedOffset.y+0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 89 and bedHeading <= 99 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 99 and bedHeading <= 109 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 109 and bedHeading <= 119 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 119 and bedHeading <= 129 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 129 and bedHeading <= 139 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 139 and bedHeading <= 149 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 149 and bedHeading <= 159 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 159 and bedHeading <= 169 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 169 and bedHeading <= 179 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.05, bedOffset.y-0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 179 and bedHeading <= 189 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y-0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 189 and bedHeading <= 199 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 199 and bedHeading <= 209 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 209 and bedHeading <= 219 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 219 and bedHeading <= 229 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 229 and bedHeading <= 239 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 239 and bedHeading <= 249 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 249 and bedHeading <= 259 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 259 and bedHeading <= 269 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.75, bedOffset.y-0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 269 and bedHeading <= 279 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 279 and bedHeading <= 289 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 289 and bedHeading <= 299 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 299 and bedHeading <= 309 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 309 and bedHeading <= 319 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 319 and bedHeading <= 329 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 329 and bedHeading <= 339 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 339 and bedHeading <= 349 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 349 and bedHeading <= 360 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.05, bedOffset.y+0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
					end
					if bedOffset.x <= 0 and bedOffset.y >= 0 and bedOffset.z <= 0 then
						if bedHeading >= 0 and bedHeading <= 9 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y+0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 9 and bedHeading <= 19 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 19 and bedHeading <= 29 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 29 and bedHeading <= 39 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 39 and bedHeading <= 49 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 49 and bedHeading <= 59 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 59 and bedHeading <= 69 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 69 and bedHeading <= 79 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 79 and bedHeading <= 89 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.75, bedOffset.y+0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 89 and bedHeading <= 99 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 99 and bedHeading <= 109 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 109 and bedHeading <= 119 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 119 and bedHeading <= 129 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 129 and bedHeading <= 139 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 139 and bedHeading <= 149 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 149 and bedHeading <= 159 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 159 and bedHeading <= 169 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 169 and bedHeading <= 179 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.05, bedOffset.y-0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 179 and bedHeading <= 189 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y-0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 189 and bedHeading <= 199 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 199 and bedHeading <= 209 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 209 and bedHeading <= 219 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 219 and bedHeading <= 229 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 229 and bedHeading <= 239 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 239 and bedHeading <= 249 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 249 and bedHeading <= 259 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 259 and bedHeading <= 269 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.75, bedOffset.y-0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 269 and bedHeading <= 279 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 279 and bedHeading <= 289 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 289 and bedHeading <= 299 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 299 and bedHeading <= 309 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 309 and bedHeading <= 319 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 319 and bedHeading <= 329 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 329 and bedHeading <= 339 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 339 and bedHeading <= 349 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 349 and bedHeading <= 360 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.05, bedOffset.y+0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
					end
					if bedOffset.x >= 0 and bedOffset.y <= 0 and bedOffset.z <= 0 then
						if bedHeading >= 0 and bedHeading <= 9 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y+0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 9 and bedHeading <= 19 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 19 and bedHeading <= 29 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 29 and bedHeading <= 39 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 39 and bedHeading <= 49 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 49 and bedHeading <= 59 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 59 and bedHeading <= 69 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 69 and bedHeading <= 79 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 79 and bedHeading <= 89 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.75, bedOffset.y+0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 89 and bedHeading <= 99 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 99 and bedHeading <= 109 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 109 and bedHeading <= 119 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 119 and bedHeading <= 129 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 129 and bedHeading <= 139 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 139 and bedHeading <= 149 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 149 and bedHeading <= 159 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 159 and bedHeading <= 169 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 169 and bedHeading <= 179 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.05, bedOffset.y-0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 179 and bedHeading <= 189 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y-0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 189 and bedHeading <= 199 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 199 and bedHeading <= 209 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 209 and bedHeading <= 219 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 219 and bedHeading <= 229 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 229 and bedHeading <= 239 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 239 and bedHeading <= 249 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 249 and bedHeading <= 259 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 259 and bedHeading <= 269 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.75, bedOffset.y-0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 269 and bedHeading <= 279 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 279 and bedHeading <= 289 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 289 and bedHeading <= 299 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 299 and bedHeading <= 309 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 309 and bedHeading <= 319 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 319 and bedHeading <= 329 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 329 and bedHeading <= 339 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 339 and bedHeading <= 349 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 349 and bedHeading <= 360 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.05, bedOffset.y+0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
					end
					if bedOffset.x <= 0 and bedOffset.y <= 0 and bedOffset.z <= 0 then
						if bedHeading >= 0 and bedHeading <= 9 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y+0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 9 and bedHeading <= 19 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 19 and bedHeading <= 29 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 29 and bedHeading <= 39 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 39 and bedHeading <= 49 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 49 and bedHeading <= 59 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 59 and bedHeading <= 69 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 69 and bedHeading <= 79 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 79 and bedHeading <= 89 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.75, bedOffset.y+0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 89 and bedHeading <= 99 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 99 and bedHeading <= 109 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 109 and bedHeading <= 119 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 119 and bedHeading <= 129 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 129 and bedHeading <= 139 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 139 and bedHeading <= 149 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 149 and bedHeading <= 159 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 159 and bedHeading <= 169 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 169 and bedHeading <= 179 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x-0.05, bedOffset.y-0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 179 and bedHeading <= 189 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x, bedOffset.y-0.8, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 189 and bedHeading <= 199 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y-0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 199 and bedHeading <= 209 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y-0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 209 and bedHeading <= 219 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y-0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 219 and bedHeading <= 229 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y-0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 229 and bedHeading <= 239 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y-0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 239 and bedHeading <= 249 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y-0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 249 and bedHeading <= 259 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y-0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 259 and bedHeading <= 269 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.75, bedOffset.y-0.05, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 269 and bedHeading <= 279 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.8, bedOffset.y, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 279 and bedHeading <= 289 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.7, bedOffset.y+0.1, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 289 and bedHeading <= 299 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.6, bedOffset.y+0.2, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 299 and bedHeading <= 309 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.5, bedOffset.y+0.3, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 309 and bedHeading <= 319 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.4, bedOffset.y+0.4, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 319 and bedHeading <= 329 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.3, bedOffset.y+0.5, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 329 and bedHeading <= 339 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.2, bedOffset.y+0.6, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 339 and bedHeading <= 349 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.1, bedOffset.y+0.7, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
						if bedHeading >= 349 and bedHeading <= 360 then
							FreezeEntityPosition(playerPed, true)
							SetEntityCoords(playerPed, bedOffset.x+0.05, bedOffset.y+0.75, bedOffset.z+0.8)
							SetEntityHeading(playerPed, bedHeading-90)
						end
					end
					--end
						RequestAnimDict("timetable@tracy@sleep@")
						while not HasAnimDictLoaded("timetable@tracy@sleep@") do
							Citizen.Wait(0)
						end
						TaskPlayAnim(PlayerPedId(), "timetable@tracy@sleep@", "idle_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
						DoScreenFadeOut(5000)
						Citizen.Wait(Config.Sleep)
						TaskPlayAnim(PlayerPedId(), "timetable@tracy@sleep@", "idle_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
						DoScreenFadeIn(5000)
						Citizen.Wait(5000)
						FreezeEntityPosition(player, false)
						RequestAnimDict("switch@franklin@bed")
						while not HasAnimDictLoaded("switch@franklin@bed") do
							Citizen.Wait(0)
						end
						TaskPlayAnim(PlayerPedId(), "switch@franklin@bed", "sleep_getup_rubeyes", 8.0, -8.0, -1, 0, 0.0, false, false, false)
						Citizen.Wait(9000)
						if Config.Health then
						SetEntityHealth(player, 200)
						end
						if Config.DecreaseHungerAndThirst then
							TriggerServerEvent("podol:sleep")
						end
						ClearPedTasks(player)
						
						if Config.RemovePajama then
							RequestAnimDict("clothingtrousers")
						while not HasAnimDictLoaded("clothingtrousers") do
							Citizen.Wait(0)
						end
						TaskPlayAnim(PlayerPedId(), "clothingtrousers", "try_trousers_neutral_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
							ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin)
								TriggerEvent('skinchanger:loadSkin', skin)
							end)
						end
						bedOpen = false
						break
		end
	end
end)


-- 3D Text function
function Draw3DText(x, y, z, text, scale)
	local onScreen, _x, _y = World3dToScreen2d(x, y, z)
	local pX, pY, pZ = table.unpack(GetGameplayCamCoords())
	SetTextScale(scale, scale)
	SetTextFont(4)
	SetTextProportional(1)
	SetTextEntry('STRING')
	SetTextCentre(true)
	SetTextColour(255, 255, 255, 215)
	AddTextComponentString(text)
	DrawText(_x, _y)
	local factor = (string.len(text)) / 700
	DrawRect(_x, _y + 0.0150, 0.06 + factor, 0.03, 41, 11, 41, 100)
end

-- Check in/check out main core
Citizen.CreateThread(function()
	Citizen.Wait(1000)
	local location = Config.Location
	while true do
		Citizen.Wait(Config.Optimization)
		local player = GetPlayerPed(-1)
		local letSleep = true
		--if ESX.PlayerData.job ~= nil then
			local coords = GetEntityCoords(PlayerPedId())
			if IsNear then
				letSleep = false
				if pregnant2 then
					Draw3DText(location.x, location.y, location.z, (_U('childbirth')), 0.4)
					if Vdist(coords, location) < 1 and IsControlJustReleased(1, 38) then
						if Config.UseMythicProgbar then
						SetEntityHeading(player, Config.Heading)
						TriggerEvent('mythic_progbar:client:progress', {
							name = 'pregnant',
							duration = Config.Pregnant,
							label = (_U('childbirth2')),
							useWhileDead = false,
							canCancel = false,
							controlDisables = {
								disableMovement = true,
								disableCarMovement = true,
								disableMouse = false,
								disableCombat = true,
							},
							animation = {
								animDict = 'random@mugging4',
								anim = 'flee_backward_loop_shopkeeper',
							}
						}, function(status)
							if not status then
								local togel2 = math.random(1,Config.Chance2)
								local player = GetPlayerPed(-1)
								RequestAnimDict("switch@franklin@bed")
								while not HasAnimDictLoaded("switch@franklin@bed") do
									Citizen.Wait(0)
								end
								TaskPlayAnim(PlayerPedId(), "switch@franklin@bed", "sleep_getup_rubeyes", 8.0, -8.0, -1, 0, 0.0, false, false, false)
								Citizen.Wait(9000)
								ClearPedTasks(player)
								pregnant = false
								pregnant2 = false
								RequestAnimDict("clothingtrousers")
								while not HasAnimDictLoaded("clothingtrousers") do
									Citizen.Wait(0)
								end
								TaskPlayAnim(PlayerPedId(), "clothingtrousers", "try_trousers_neutral_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
								ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin)
									TriggerEvent('skinchanger:loadSkin', skin)
								end)
								TriggerEvent('podol:removepregnantStatus')
								ESX.ShowNotification(_U('notpregnantanymore'))
								if Config.Death then
									if togel2 == 1 then
										SetEntityHealth(player, 0)
									end
								end
							end
						end)
						else
								SetEntityHeading(player, Config.Heading)
								local togel2 = math.random(1,Config.Chance2)
								local player = GetPlayerPed(-1)
								RequestAnimDict("random@mugging4")
								while not HasAnimDictLoaded("random@mugging4") do
									Citizen.Wait(0)
								end
								TaskPlayAnim(PlayerPedId(), "random@mugging4", "flee_backward_loop_shopkeeper", 8.0, -8.0, -1, 0, 0.0, false, false, false)
								Citizen.Wait(Config.Pregnant)
								RequestAnimDict("switch@franklin@bed")
								while not HasAnimDictLoaded("switch@franklin@bed") do
									Citizen.Wait(0)
								end
								TaskPlayAnim(PlayerPedId(), "switch@franklin@bed", "sleep_getup_rubeyes", 8.0, -8.0, -1, 0, 0.0, false, false, false)
								Citizen.Wait(9000)
								ClearPedTasks(player)
								pregnant = false
								pregnant2 = false
								RequestAnimDict("clothingtrousers")
								while not HasAnimDictLoaded("clothingtrousers") do
									Citizen.Wait(0)
								end
								TaskPlayAnim(PlayerPedId(), "clothingtrousers", "try_trousers_neutral_c", 8.0, -8.0, -1, 0, 0.0, false, false, false)
								ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin)
									TriggerEvent('skinchanger:loadSkin', skin)
								end)
								TriggerEvent('podol:removepregnantStatus')
								ESX.ShowNotification(_U('notpregnantanymore'))
								if Config.Death then
									if togel2 == 1 then
										SetEntityHealth(player, 0)
									end
								end
						end
					end
				end
			end
		--end
		if letSleep then
		Citizen.Wait(500)
		end
	end
end)

RegisterCommand(Config.CommandShower, function(source, args, raw)

	ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin, jobSkin)
							if skin.sex == 0 then
								local clothesSkin = Config.MaleShower
								TriggerEvent('skinchanger:loadClothes', skin, clothesSkin)
							else
								local clothesSkinfemale = Config.FemaleShower
									TriggerEvent('skinchanger:loadClothes', skin, clothesSkinfemale)
							end
						end)
						local coords = GetEntityCoords(PlayerPedId())
						encour = true
						FreezeEntityPosition((PlayerPedId()), true)
						if not HasNamedPtfxAssetLoaded("core") then
							RequestNamedPtfxAsset("core")
							while not HasNamedPtfxAssetLoaded("core") do
								Wait(1)
							end
						end
						--TaskStartScenarioInPlace((PlayerPedId()), "PROP_HUMAN_STAND_IMPATIENT", 0, true)
						RequestAnimDict("mp_safehouseshower@male@")
						while not HasAnimDictLoaded("mp_safehouseshower@male@") do
							Citizen.Wait(100)
						end
						TaskPlayAnim(GetPlayerPed(-1), "mp_safehouseshower@male@", "male_shower_idle_a", 1.0, 4.0, -1, 0, 0, false, false, false)
						--Citizen.Wait(5000)

						UseParticleFxAssetNextCall("core") 
						particles  = StartNetworkedParticleFxNonLoopedAtCoord("ent_sht_water", coords.x, coords.y, coords.z +1.4, 0.0, 0.0, 0.0, 1.0, false, false, false, false) 
						UseParticleFxAssetNextCall("core") 
						Citizen.Wait(3000) 
						--TaskPlayAnim(GetPlayerPed(-1), "mp_safehouseshower@male@", "male_shower_idle_a", 1.0, 4.0, 3000, 0, 0, false, false, false)
						particles2  = StartNetworkedParticleFxNonLoopedAtCoord("ent_sht_water", coords.x, coords.y, coords.z +1.4, 0.0, 0.0, 0.0, 1.0, false, false, false, false) 
						UseParticleFxAssetNextCall("core") 
						Citizen.Wait(3000) 
						--TaskPlayAnim(GetPlayerPed(-1), "mp_safehouseshower@male@", "male_shower_idle_a", 1.0, 4.0, 3000, 0, 0, false, false, false)
						particles3  = StartNetworkedParticleFxNonLoopedAtCoord("ent_sht_water", coords.x, coords.y, coords.z +1.4, 0.0, 0.0, 0.0, 1.0, false, false, false, false) UseParticleFxAssetNextCall("core") 
						Citizen.Wait(3000) 
						TaskPlayAnim(GetPlayerPed(-1), "mp_safehouseshower@male@", "male_shower_idle_a", 1.0, 4.0, -1, 0, 0, false, false, false)
						particles4  = StartNetworkedParticleFxNonLoopedAtCoord("ent_sht_water", coords.x, coords.y, coords.z +1.4, 0.0, 0.0, 0.0, 1.0, false, false, false, false) 
						UseParticleFxAssetNextCall("core") 
						Citizen.Wait(3000) 
						particles5  = StartNetworkedParticleFxNonLoopedAtCoord("ent_sht_water", coords.x, coords.y, coords.z +1.4, 0.0, 0.0, 0.0, 1.0, false, false, false, false) 
						timer = 8
						sortir = true
						Citizen.CreateThread(function()
							while sortir do
								Citizen.Wait(0)
								Citizen.Wait(1000)
								if(timer > 0)then
									timer = timer - 1
								elseif (timer == 0) then
									encour = false
									FreezeEntityPosition((PlayerPedId()), false)
									ESX.ShowNotification(_U('shower'))
									if Config.AddHealthAfterShower then
										local playerPed = GetPlayerPed(-1)
										local maxHealth = GetEntityMaxHealth(playerPed)
										local health = GetEntityHealth(playerPed)
										local newHealth = math.min(maxHealth, math.floor(health + maxHealth/Config.ValueHealthAfterShower))
										SetEntityHealth(playerPed, newHealth)
									end
									if Config.AddArmorAfterShower then
										AddArmourToPed(GetPlayerPed(-1), Config.ValueArmorAfterShower)
									end
									ClearPedSecondaryTask(GetPlayerPed(-1))
									ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin, jobSkin)
										TriggerEvent('skinchanger:loadSkin', skin)
									end)
									StopParticleFxLooped(particles, 0) 
									StopParticleFxLooped(particles2, 0) 
									StopParticleFxLooped(particles3, 0) 
									StopParticleFxLooped(particles4, 0) 
									--StopParticleFxLooped(particles5, 0)
									sortir = false
								end
							end
						end)
end, false)
						

-- Draw distance
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(1000)
		local coords = GetEntityCoords(PlayerPedId())
		if Vdist(coords, Config.Location) < 5 then
			IsNear = true
		else
			IsNear = false
		end
	end
end)

function DisplayHelpText(str)
	SetTextComponentFormat("STRING")
	AddTextComponentString(str)
	DisplayHelpTextFromStringLabel(0, 0, 1, -1)
end