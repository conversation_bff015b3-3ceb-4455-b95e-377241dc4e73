Config = {}

Config.Framework = 'ESX' -- ESX or QB
Config.FrameworkCore = 'es_extended' -- es_extended, qb-core, if-core, ...

Config.Notification = function(message, type)
    if Config.Framework:lower() == 'esx' then
        TriggerEvent('esx:showNotification', message, type, 2500)
    elseif Config.Framework:lower() == 'qb' or Config.Framework:lower() == 'qbcore' or Config.Framework:lower() == 'qb-core' then
        TriggerEvent('QBCore:Notify', message, type, 5000)
    end
end

Config.PayMethod = {'money', 'bank'} -- Payment methods to check: 'money'/'cash' and/or 'bank'

Config.Items = {
    ['phone'] = { -- Item
        category = "category_1", -- Category
        display = "Telefono", -- Display Name
        price = 500, -- Price per unit
        description = "Habla con quien tu quieras...", -- Description
    },
    ['radio'] = {
        category = "category_1",
        display = "Radio",
        price = 200,
        description = "Usa la radio para todo...",
    },
    ['mradio'] = {
        category = "category_1",
        display = "Radio De Carro",
        price = 500,
        description = "Radio de carro...",
    },
    ['phone_charger'] = {
        category = "category_1",
        display = "Cargador de Telefono",
        price = 100,
        description = "No te quedes sin bateria...",
    },
    ['hitman_tablet'] = {
        category = "category_1",
        display = "Hitman Tablet",
        price = 600,
        description = "Arregla tu vida y la de los demas...",
    },
    ['tvremote'] = {
        category = "category_1",
        display = "Control De Television",
        price = 100,
        description = "No olvides ver tu programa favorito..",
    },
    ['conedeck'] = {
        category = "category_2",
        display = "Carta Uno",
        price = 50,
        description = "Que gane el mejor..",
    },
}

Config.Locale = {
    buyed = "You made a purchase, paid $%s.",
    nomoney = "You can't afford the purchase!",
    Interact = "Press ~INPUT_CONTEXT~ to access the Shop",
}

Config.Shops = {
    { x = -529.03,   y = -584.13,   z = 33.68, blipname = "Store", blipsprite = 590, blipcolor = 0, blipscale = 0.5, dist = 2.5 },

}