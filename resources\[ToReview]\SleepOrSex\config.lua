Config = {}
local second = 1
local minute = 60 * second
local hour = 60 * minute
local day = 24 * hour
local week = 7 * day
local month = 4 * week

Config.TimePregnant = 10 * day --How long until u get big pregnant? example u want add 10 days (10 * days) or (week + 3 * day)

Config.Locale = 'en' -- What language did you want to use? Only available english btw
Config.TriggerEvent = 'esx:getSharedObject' -- Default one

Config.Sleep = 15000 --Time of sleep (miliseconds)
Config.BlowJobStand = 15000 --Time of BlowJob (miliseconds)
Config.SexStand = 15000 --Time of BlowJob (miliseconds)
Config.Doggy = 15000 --Time of Doggy (miliseconds)
Config.BlowJob = 15000 --Time of BlowJob on the bed (miliseconds)
Config.WoT = 15000 --Time of WoT (miliseconds)
Config.WoT2 = 15000 --Time of WoT2 (miliseconds)
Config.WoT3 = 15000 --Time of WoT2 (miliseconds)
Config.ViagraExtraTime = 5000 --Extra time sex for viagra (miliseconds)
Config.Health = true --Make full health after sleeping
Config.Sex = true --Set the target and you full health after have sex
Config.DecreaseHungerAndThirst = true --Make you hunger and thirst after sleep
Config.DecreaseHungerAndThirstAfterSex = true --Set the target and you hunger and thirst after have sex
Config.Hunger = 0 --value of remove hunger (full: 1000000)
Config.Thirst = 0 --value of remove thirst (full: 1000000)
Config.UsePajama = true --Use pajama before sleep?
Config.RemovePajama = true --Remove pajama after sleep?
Config.RemoveClothes = true --Remove clothes before sex?
Config.UseClothes = true --Use clothes after sex?
Config.AllowPregnant = true --Can pregnant after sex?
Config.Death = true --Can death after childbirth?
Config.Chance = 4 --The lower it is, the more it has a chance to make the girl pregnant. (1 = 100%, 2 = 50%, 4 = 25%, 10 = 10%, 100 = 1%, etc)
Config.Chance2 = 100 --The lower it is, the more it has a chance to make the girl die when give a birth. (1 = 100%, 2 = 50%, 4 = 25%, 10 = 10%, 100 = 1%, etc)
Config.Optimization	= 4 -- Keep this between 3-6, 4-5 is recommended, DON'T go over 6
Config.Pregnant = 30000 --How long the girl will get out the child? (in miliseconds)
Config.Location = vector3(317.74, -584.94, 44.2) -- Pregnant location (put in the bed of hospital)
Config.Heading = 161.96 -- Heading Pregnant location
Config.Bed = {"v_res_msonbed_s", "p_lestersbed_s", "p_mbbed_s", "p_v_res_tt_bed_s", "v_res_msonbed_s", "v_res_mbbed", "v_res_d_bed", "v_res_tre_bed1", "v_res_tre_bed2", "v_res_tt_bed", "apa_mp_h_bed_double_09", "apa_mp_h_yacht_bed_02", "apa_mp_h_bed_double_08", "apa_mp_h_bed_with_table_02"} --What props bed did you want to use to sleep?
Config.Condom = 15000 --Time of Condom will active (miliseconds)
Config.Levonorgestrel = 15000 --Time of Levonorgestrel will active (miliseconds)
Config.Abortion = 15000 --Time of Abortion will active and delete pregnant (miliseconds)
Config.AnimMillSecPregnant = math.random(100000, 500000) --Time of Animation will active randomly while positive (miliseconds)
Config.ItemViagra = 'viagra' --Default Name Item viagra // set false if you didn't want to use item for sex everywhere
Config.Command = 'sexeverywhere' --Default Name Command Sex Everywhere
Config.TriggerEventDrink = 'esx_basicneeds:onDrink' --Default One For Drink Animation
Config.CommandShower = 'shower' --Default One For Drink Animation
Config.AddHealthAfterShower = true --Add health after shower?
Config.AddArmorAfterShower = true --Add armor after shower?
Config.ValueHealthAfterShower = 20 --Value health after shower? (Maxhealth/Config.ValueHealthAfterShower)
Config.ValueArmorAfterShower = 50 --Value armor after shower? Max: 200
--Config.UseMythicProgbar = true --Did you use Mythic Progbar?

Config.Pajama = { --Pajama Outfit
		male = {
            tshirt_1 = 15,  tshirt_2 = 0,
            torso_1 = 144,   torso_2 = 0,
            decals_1 = 0,   decals_2 = 0,
            arms = 6,
            pants_1 = 65,   pants_2 = 0,
            shoes_1 = 34,   shoes_2 = 0,
            helmet_1 = -1,  helmet_2 = 0,
            chain_1 = 0,    chain_2 = 0,
            glasses_1 = 0,     glasses_2 = 0
        },

	female = {
            tshirt_1 = 6,  tshirt_2 = 0,
            torso_1 = 142,   torso_2 = 0,
            decals_1 = 0,   decals_2 = 0,
            arms = 6,
            pants_1 = 67,   pants_2 = 0,
            shoes_1 = 35,   shoes_2 = 0,
            helmet_1 = -1,  helmet_2 = 0,
            chain_1 = 0,    chain_2 = 0,
            glasses_1 = 5,     glasses_2 = 0
        }
}

Config.Naked = { --Naked Outfit
		male = {
            tshirt_1 = 85,  tshirt_2 = 0,
            torso_1 = 15,   torso_2 = 0,
            decals_1 = 0,   decals_2 = 0,
            arms = 15,
            pants_1 = 72,   pants_2 = 0,
            shoes_1 = 34,   shoes_2 = 0,
            helmet_1 = -1,  helmet_2 = 0,
            chain_1 = 0,    chain_2 = 0,
            glasses_1 = 0,     glasses_2 = 0
        },

	female = {
            tshirt_1 = 6,  tshirt_2 = 0,
            torso_1 = 260,   torso_2 = 0,
            decals_1 = 0,   decals_2 = 0,
            arms = 15,
            pants_1 = 41,   pants_2 = 0,
            shoes_1 = 35,   shoes_2 = 0,
            helmet_1 = -1,  helmet_2 = 0,
            chain_1 = 0,    chain_2 = 0,
            glasses_1 = 5,     glasses_2 = 0
        }
}

Config.PregnantOutfit = { --Pregnant Outfit
	female = {
            tshirt_1 = 15,  tshirt_2 = 0,
            torso_1 = 68,   torso_2 = 0,
            decals_1 = 0,   decals_2 = 0,
            arms = 0,
            pants_1 = 12,   pants_2 = 0,
            shoes_1 = 16,   shoes_2 = 1,
            helmet_1 = -1,  helmet_2 = 0,
            chain_1 = 0,    chain_2 = 0,
            glasses_1 = 5,     glasses_2 = 0
        }
}

Config.MaleShower = { -- male skins shower, change if u have addon clothes
		['bags_1'] = 0, ['bags_2'] = 0,
		['tshirt_1'] = 85, ['tshirt_2'] = 0,
		['torso_1'] = 15, ['torso_2'] = 0,
		['arms'] = 15,
		['pants_1'] = 72, ['pants_2'] = 0,
		['shoes_1'] = 34, ['shoes_2'] = 0,
		['mask_1'] = 0, ['mask_2'] = 0,
		['bproof_1'] = 0,
		['helmet_1'] = -1, ['helmet_2'] = 0,
		["decals_1"] = -1, ["decals_2"] = 0,
		['chain_1'] = 0, ['chain_2'] = 0,
		['glasses_1'] = 0, ['glasses_2'] = 0
}

Config.FemaleShower = { -- female skins shower , change if u have addon clothes
		['bags_1'] = 0, ['bags_2'] = 0,
		['tshirt_1'] = 6, ['tshirt_2'] = 0,
		['torso_1'] = 260, ['torso_2'] = 0,
		['arms'] = 15,
		['pants_1'] = 41, ['pants_2'] = 0,
		['shoes_1'] = 35, ['shoes_2'] = 0,
		['mask_1'] = 0, ['mask_2'] = 0,
		['bproof_1'] = 0,
		['helmet_1'] = -1, ['helmet_2'] = 0,
		["decals_1"] = -1, ["decals_2"] = 0,
		['chain_1'] = 0, ['chain_2'] = 0,
		['glasses_1'] = 0, ['glasses_2'] = 0
}

