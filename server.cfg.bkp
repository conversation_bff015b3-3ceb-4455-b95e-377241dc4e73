# # # # # # # # # # # # # # # # # # # # # # # # # # #
#    ___ _____  __  _    ___ ___   _   _____   __   #
#   | __/ __\ \/ / | |  | __/ __| /_\ / __\ \ / /   #
#   | _|\__ \>  <  | |__| _| (_ |/ _ \ (__ \ V /    #
#   |___|___/_/\_\ |____|___\___/_/ \_\___| |_|     #
#                                                   #
#     Discord: https://discord.esx-framework.org/   #
#     Website: https://esx-framework.org/           #
#     CFG Docs: https://aka.cfx.re/server-commands  #
# # # # # # # # # # # # # # # # # # # # # # # # # # #


# Editable Settings
# -----------------
# You can edit the following:

endpoint_add_tcp "0.0.0.0:40100"
endpoint_add_udp "0.0.0.0:40100"

#sv_listingHostOverride "fivemdev.puertoricoisland.net:40101"
set sv_listingHostOverride "fivemdev.puertoricoisland.net:40100"
sv_listingIpOverride "**************:40100"
sv_endpoints "**************:40100"
#sv_endpointOverride "**************:40100"

set sv_proxyIPRanges "************/20, ************/22, ************/22, **********/22, ************/18, *************/18, ************/20, ************/20, *************/22, ************/17, ***********/15, **********/13, **********/14, **********/13, **********/22"


sv_requestParanoia 0

sv_endpointPrivacy true

# Set Tags
sets tags "roleplay, rp, puerto rico, españa, mexico, honduras, colombia, peru, salvador, republica dominicana, chile, argentina, venezuela, español, panama, uruguay, guatemala, us, serious, optimizado, optimized, friendly, amigable"

# Optional Editable Settings
# --------------------------
# You may edit the following:

sv_licenseKey "cfxk_1BMKovtjyhJ6Evsp3W2cM_VtRXg"
sv_hostname " Puerto Rico Island Development"
sets sv_projectName " Puerto Rico Island Development"
sets sv_projectDesc "Developemnt Box"
set mysql_connection_string "mysql://server:server@************/island_development?charset=utf8mb4"
#sv_master1 ""


 set hardcap_discord_token "NjY0OTk4NDM4NzU2MTU1Mzky.Xm1YkQ.CFvQcfPtiLqP8wSfBhxhlGziCuY"
 set hardcap_discord_guild "661293435394850867"
 set hardcap_discord_roles "1360040015488483578, 802255275364319252, 663996490422681625, 663994558945951755, 1355668901580640286, 1355672461659869426"
 
# 663996490422681625   ems
# 663994558945951755    policia
# 1355668901580640286   streamer
#1355672461659869426		Canto

load_server_icon pr2.png
sv_maxclients 25
sets locale "es-PR"
sv_enforceGameBuild 3258
set resources_useSystemChat true
set SCREENSHOT_BASIC_TOKEN AhDOKZ1AxKiWBvysboymR2JbqtPE4GTs
set npwd:framework esx


# System Administrators
# ---------------------
# Add system admins
rcon_password "FuckFuckFuckFuckFuck:)"

add_principal group.admin group.user
add_principal group.user group.admin
add_ace group.admin command allow # allow all commands
add_ace group.admin command.quit deny # but don't allow quit

add_principal group.adminlow group.user
add_ace group.adminlow command allow # allow all commands
add_ace group.adminlow command.quit deny # but don't allow quit
add_ace group.adminlow command.giveitem deny #

add_ace resource.es_extended command.add_ace allow
add_ace resource.es_extended command.add_principal allow
add_ace resource.es_extended command.remove_principal allow
add_ace resource.es_extended command.stop allow

add_principal identifier.fivem:699321 group.admin #bandidom9
add_principal identifier.discord:280105640842625025 group.admin #bandidom9
add_principal identifier.discord:945742356462845953 group.admin #bandidom9

add_principal identifier.discord:533268732252782593 group.admin #Ive
add_principal identifier.fivem:2576693 group.admin #Ive

add_principal identifier.discord:732391602731089970 group.admin #Lola
add_principal identifier.fivem:13210152 group.admin #Lola

add_principal identifier.discord:543172415568609292 group.admin #Tony
add_principal identifier.fivem:670694 group.admin #Tony

add_principal identifier.discord:945742356462845953 group.admin #Johnson
add_principal identifier.fivem:7495140 group.admin #Johnson

add_principal identifier.discord:726852879138160691 group.admin #Gabriel



####IslandFreeCam

add_ace freecamgroup command.islandFreeCam allow

add_principal identifier.discord:726852879138160691 group.freecamgroup

#adminMenu
add_ace group.admin "WaveAdmin.admin" allow
add_ace group.mod "WaveAdmin.mod" allow

# pma-voice Config
# ----------------
setr voice_enableRadioAnim 1
setr voice_useNativeAudio true
setr voice_useSendingRangeOnly true

## Ox_Lib 

# https://v6.mantine.dev/theming/colors/#default-colors
setr ox:primaryColor blue
setr ox:primaryShade 8
setr ox:userLocales 1 # Allow users to select their locales using /ox_lib

add_ace resource.ox_lib command.add_ace allow
add_ace resource.ox_lib command.remove_ace allow
add_ace resource.ox_lib command.add_principal allow
add_ace resource.ox_lib command.remove_principal allow

add_ace group.admin communityservice allow


add_ace group.admin oxitems allow

exec ox.cfg



exec perms.cfg

exec scriptPerms.cfg

## Illenium
setr illenium-appearance:locale "es-ES"

#Vehicles

#set mVehicle:Persistent true  # no needed on new version is now on config


# security-convars
# ----------------
sv_enableNetworkedSounds false
sv_enableNetworkedScriptEntityStates false
sv_enableNetworkedPhoneExplosions false
sv_filterRequestControl 2

# ESX Language (edit to change the language of ESX)
# -----------------------------------------------------------
## Umcomment to set your own locale,
## if not, it will use the language you have selected in txAdmin.
setr esx:locale "es"

# Default & Standalone Resources
# ------------------------------
ensure chat
ensure hardcap
ensure oxmysql
ensure ox_lib
# ESX Legacy Core
# ----------
ensure [core]
ensure pma-voice
ensure illenium-appearance

ensure balenciaga_weapon_packv7

ensure ox_target
ensure ox_inventory
ensure lb-phoneprop
ensure lb-upload
ensure lb-phone
ensure js_lbcharg
ensure ox_doorlock

ensure [minigames]
ensure qs-jobs-creator

ensure [Libs]
ensure t1ger_lib

#ensure mVehicle
#ensure mGarage

# ESX Addons
# ----------


ensure community_bridge
ensure MrNewbVehicleKeys

ensure [esx_addons]
ensure [standalone]
ensure wasabi_bridge
ensure wasabi_ambulance



#ensure mVehshop


ensure t1ger_carlift
ensure t1ger_mechanic
ensure t1ger_tuningsystem



ensure fivem-greenscreener


ensure [weapons]

ensure [Jobs]

#props anims
ensure brum_gold
#ensure streamOnly
#ensure rpemotes
ensure dpemotesv2

#maps Stuff

ensure [roxwood]
ensure [utopia_gardens]

#Map Patch
ensure [cassidy_b157]
ensure rags-and-riches

ensure [ThemePark]
ensure [Maps]
ensure [Quasar]
ensure motorhome-vehicles
ensure [MarketShop]

ensure [Nuevos]


ensure [r_script]
ensure [OpenAccess]
ensure Props
ensure adaiaclothes
ensure adaiaclothesv1
ensure adaiaclothesv2
ensure adaiaclothesv3
ensure adaiaclothesv4
ensure demon_juicy_pack
ensure kaydenclothes
ensure kaydenclothesv1
ensure dlc-clothes
ensure demon_juicy_update_v3



#Vehicles
ensure SinzIslandCarPack
ensure pdesign3
ensure PedroDesingNoLogo
ensure BandiTranportation

ensure rtx_waterpark_roxwood
ensure rtx_wateractivities
ensure generic_texture_renderer_gfx
ensure rtx_tv

ensure codewave-pregnancy-tracker-new
ensure cdev_cOne
ensure rtx_gym
ensure BakiTelli_hideandseek

ensure 0r-hud-v3

ensure bMenu

ensure PB_ChemistryV1
ensure Bandi-Civil

ensure [Bandi]

ensure [rcore]

ensure pug-robberycreator

ensure [MultiGames]


ensure [Sex]
stop Kink