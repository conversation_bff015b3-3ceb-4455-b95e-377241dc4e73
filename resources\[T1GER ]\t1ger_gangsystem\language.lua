Lang = {

	-- Notifications:
	['not_enough_police'] = 'Not enough police online.',
	['not_enough_moeny'] = 'Not enough money.',
	['no_players_nearby'] = 'No players nearby.',
	['only_gang_members'] = 'Action only possible for gang-members.',
	['no_drugs_to_sell'] = 'You have no drugs on you to sell.',
	['possible_drug_sale_at'] = 'Possible drug-sale on-going at: %s',
	['drug_sale_rejected'] = 'The NPC rejected your drugs.',
	['drug_sale_reward'] = 'You received $%s, for %sx of %s.',
	['gang_created'] = 'You created a gang: %s.',
	['no_gangs'] = 'There aren\'t any gangs created yet.',
	['enter_gang_name'] = 'Please enter a name for the gang.',
	['enter_player_id'] = 'Please enter an integer for Player ID.',
	['incorrect_player_id'] = 'The entered Player ID is incorrect, try again.',
	['input_amount_higher_0'] = 'Entered amount must be higher than 0.',
	['input_required'] = 'You must provide an input.',
	['gang_deleted'] = 'You deleted a gang: %s.',
	['gang_leader_updated'] = 'Gang leader of %s has been set to: %s.',
	['gang_disabled'] = 'You disabled the gang: %s',
	['gang_enabled'] = 'You enabled the gang: %s',
	['your_gang_is_disabled'] = 'Your gang has been disabled, contact admin.',
	['no_player_gang'] = 'You are not a member of any gangs.',
	['no_gang_permission'] = 'You have insufficient perms in your gang to do this.',
	['leave_gang'] = 'You left your current gang.',
	['cannot_kick_leader'] = 'You cannot kick the gang leader, idiot.',
	['kicked_gang_member'] = 'You kicked %s from the gang.',
	['cannot_demote_leader'] = 'You cannot demote/promote the gang leader, idiot.',
	['member_rank_updated'] = 'You updated %s\'s gang-rank.',
	['gang_invitation_sent'] = 'You sent a gang invitation to: %s.',
	['member_already_in_gang'] = 'The player is already in a gang.',
	['invitation_accepted'] = 'You accepted the gang invitation.',
	['invitation_declined'] = 'You declined the gang invitation.',
	['invitation_accepted2'] = 'Invited member has accepted the invitation and joined the gang.',
	['invitation_declined2'] = 'Invited member has declined the invitation.',
	['rank_does_not_match'] = 'Entered Rank does not match...',
	['rank_name_updated'] = 'Rank name updated to: %s',
	['marker_cooldown'] = 'You currently have a marker cooldown on this type, wait: %s minutes.',
	['marker_created'] = 'You created a marker.',
	['marker_deleted'] = 'You deleted a marker.',
	['pin_code_not_match'] = 'PIN-Code does not match.',
	['pin_code_changed'] = 'You have changed the Pin-Code.',
	['pin_code_required'] = 'Pin-Code Required.',
	['pin_code_attempts'] = 'Pin-Code Attempts [%s] exceeded, wait: %s minutes.',
	['pin_code_incorrect'] = 'Pin-Code Incorrect | Access Denied',
	['pin_code_correct'] = 'Pin-Code Correct | Access Granted',
	['no_owned_vehicles'] = 'You have no owned vehicles.',
	['cash_deposited'] = 'You deposited $%s into the gang cash locker.',
	['cash_withdrawn'] = 'You withdrew $%s from the gang cash locker.',
	['cash_withdraw_max'] = 'You cannot withdraw more than what\'s in the locker.',
	['not_enough_notoriety'] = 'Not enough gang notoriety to do this.',
	['not_enough_gang_cash'] = 'Not enough cash in gang locker to do this.',
	['no_jobs_available'] = 'No jobs available.',
	['already_has_job'] = 'You already have a job.',
	['no_ongoing_job'] = 'You have no ongoing jobs to cancel.',
	['job_cancelled'] = 'You cancelled the current ongoing job.',
	['pickup_hooker_at'] = 'Pick-Up the hooker at %s',
	['hooker_died'] = 'The hooker died, job failed.',
	['hooker_engine_dmg'] = 'Engine got damaged more than 20%, you scared the hooker.',
	['hooker_honk_to_call'] = 'Honk with your horn to call the hooker.',
	['hooker_drop_me_at'] = 'Please drop me off at %s.',
	['hooker_seat_occupied'] = 'Vehicle front-seat is occupied.',
	['hooker_drop_off'] = 'Thank you for the ride darling.',
	['hooker_reward'] = '$%s, will be deposited to your gang cash locker.',
	['racket_paid'] = 'You paid: %s notoriety points and claimed the racket.',
	['racket_cost'] = 'This racket costs: %s notoriety points. Your gang notoriety: %s',
	['racket_withdraw'] = 'You withdrew the selected racket.',
	['collected_requested'] = 'You successfully requested a collection, come back later to collect the cash.',
	['collection_not_ready'] = 'Collection is not ready yet, check back later.',
	['cash_collected'] = 'You collected: $%s, which will be added to your gang cash locker.',
	['shop_has_collection'] = 'A shop has protection money ready to be collected',
	['you_are_ziptied'] = 'You are ziptied, actions not possible.',
	['target_not_ziptied'] = 'Target player is not zip tied.',
	['inv_need_zipties'] = 'You need a pair of zipties in your inventory to do this!',
	['inv_need_headbag'] = 'You need a headbag in your inventory.',
	['hostage_weapon'] = 'You need to hold a weapon to take hostage.',
	['notoriety_plus'] = 'Gang notoriety increased with +%s',
	['notoriety_minus'] = 'Gang notoriety decreased with -%s',
	['gang_cash_plus'] = 'Gang cash increased with +$%s',
	['gang_cash_minus'] = 'Gang cash decreased with -$%s',

	-- TARGETS:
	['access_rackets'] = 'Access Rackets',
	['target_sell_drugs'] = 'Sell Drugs',

	-- DRAW TEXTS:
	['request_collection'] = 'Request Collection',
	['collect_protection_money'] = 'Collect Cash',


	-- CONTEXT MENU TITLE:
	['confirm'] = 'Yes',
	['decline'] = 'No',
	['gang_admin_menu'] = 'Gangs [Admin]',
	['gang_admin_menu'] = 'Gangs [Admin]',
	['view_gangs'] = 'View Gangs',
	['create_gang'] = 'Create Gang',
	['view_gangs_admin'] = 'View Gangs [Admin]',
	['manage_gang'] = 'Manage Gang: %s',
	['gang_notoriety_x'] = 'Gang Notoriety: %s',
	['title_notoriety_p_m'] = 'Notoriety Plus/Minus',
	['title_cash_p_m'] = 'Cash Plus/Minus',
	['title_delete_gang'] = 'Delete Gang',
	['title_disable_gang'] = 'Enable/Disable Gang',
	['title_change_leader'] = 'Change Leader',
	['action_plus'] = 'Plus',
	['action_minus'] = 'Minus',
	['gang_cash_locker'] = 'Gang Cash Locker: %s',
	['delete_gang_title'] = 'Delete Gang?',
	['player_id'] = 'Player ID',
	['change_gang_leader'] = 'Change Gang Leader',
	['disable_enable_gang'] = 'Disable/Enable Gang',
	['disable_gang'] = 'Disable Gang',
	['enable_gang'] = 'Enable Gang',
	['gang_details'] = 'Gang Details',
	['gang_members'] = 'Gang Members',
	['gang_invite'] = 'Invite Member',
	['gang_ranks'] = 'Ranks',
	['gang_marker_management'] = 'Marker Management',
	['gang_jobs'] = 'Jobs',
	['gang_actions'] = 'Actions',
	['gang_leave'] = 'Leave Gang',
	['gang_dissolve'] = 'Dissolve Gang',
	['gang_kick_member'] = 'Kick Member',
	['gang_promote_member'] = 'Promote/Demote',
	['gang_accept_inv'] = 'Accept Invitation - ',
	['delete_marker'] = 'Delete Marker',
	['change_pin'] = 'Change PIN',
	['create_x_marker'] = 'Create %s Marker',
	['manage_x_marker'] = 'Manage %s',
	['gang_garage'] = 'Garage',
	['garage_get_veh'] = 'Get Vehicle',
	['garage_store_veh'] = 'Store Vehicle',
	['garage_vehicles'] = 'Vehicles',
	['cash_locker'] = 'Cash Locker',
	['cash_balance'] = 'Balance',
	['cash_deposit'] = 'Deposit',
	['cash_withdraw'] = 'Withdraw',
	['cash_cur_balance'] = 'Current Balance',
	['cash_deposit_amount'] = 'Deposit Amount',
	['cash_withdraw_amount'] = 'Withdraw Amount',
	['gang_jobs'] = 'Jobs',
	['gang_request_job'] = 'Request Job',
	['gang_cancel_job'] = 'Cancel Job',
	['gang_select_racket'] = 'Select Racket',
	['npc_rackets'] = 'Rackets',
	['npc_claim_racket'] = 'Claim [%s points]',
	['npc_racket_withdraw'] = 'Withdraw',
	['zip_tie_hands'] = 'Zip Tie Hands',
	['remove_zip_ties'] = 'Remove Zip Ties',
	['action_escort'] = 'Escort',
	['action_search'] = 'Search',
	['action_vehicle_in'] = 'Vehicle In',
	['action_vehicle_out'] = 'Vehicle Out',
	['action_trunk_in'] = 'Trunk In',
	['action_trunk_out'] = 'Trunk Out',
	['action_headbag'] = 'Headbag',
	['action_hostage'] = 'Hostage',
	['action_release_hostage'] = 'Release Hostage',
	['action_kill_hostage'] = 'Kill Hostage',
	['action_menu'] = 'Action Menu',

	-- CONTEXT DESCRIPTIONS:
	['click_to_view_gangs'] = 'Click to view all gangs',
	['click_to_create gang'] = 'Create Gang',
	['click_to_view_selected_gang'] = 'Click to view the gang',
	['click_to_notoriety_p_m'] = 'Click to plus/minus notoriety points',
	['click_to_cash_p_m'] = 'Click to plus/minus gang cash',
	['click_to_delete_gang'] = 'Click to delete this gang',
	['click_to_change_leader'] = 'Click to change gang leader',
	['click_to_enable_disable_gang'] = 'Click to enable or disable the gang',
	['click_to_cancel_return'] = 'Click to cancel and return.',
	['description_disable'] = 'Block all access to the gang without deleting.',
	['description_enable'] = 'Unblock access to the gang.',
	['description_g_details'] = 'Hover to see gang details.',
	['desc_view_manage_gang'] = 'View & manage gang-members',
	['desc_inv_member'] = 'Invite member to gang',
	['desc_manage_ranks'] = 'Click to manage gang ranks',
	['desc_marker_manage'] = 'Create markers for stash, garage and locker',
	['desc_request_job'] = 'Request job or cancel on-going job',
	['desc_actions'] = 'Various actions members can perform',
	['desc_leave'] = 'Click to leave your gang!',
	['desc_dissolve'] = 'Click to delete your gang!',
	['desc_kick'] = 'Click to kick member from gang',
	['desc_promote'] = 'Click to manage member ranks',
	['desc_send_inv'] = 'Click to send gang invitation.',
	['desc_same_perms'] = 'Same perms as the leader.',
	['desc_inv_and_kick'] = 'Can invite & kick members.',
	['desc_set_new_pin'] = 'Set a new PIN-Code.',
	['desc_confirm_new_pin'] = 'Confirm the new PIN-Code.',
	['hover_marker_cost'] = 'Hover to see marker costs to create.',
	['desc_m_coords'] = 'Marker coords based on player position.',
	['desc_m_pincode'] = 'Set a PIN-Code.',
	['desc_m_type'] = 'Set marker type using number',
	['desc_m_bob_up_down'] = 'Whether or not the marker should slowly animate up/down',
	['desc_m_face_cam'] = 'Whether the marker should be a billboard, as in, should constantly face the camera',

	-- INPUT DIALOGS:
	['input_gang_name'] = 'Gang Name: ',
	['input_leader_player_id'] = '(Gang Leader) Player ID: ',
	['input_plus_n'] = 'Plus Notoriety',
	['input_minus_n'] = 'Minus Notoriety',
	['input_plus_c'] = 'Plus Gang Cash',
	['input_minus_c'] = 'Minus Gang Cash',
	['input_amount'] = 'Amount',
	['input_d_enter_n_points'] = 'Enter notoriety points.',
	['input_d_enter_cash_add'] = 'Enter cash to add.',
	['input_d_enter_cash_remove'] = 'Enter cash to remove.',
	['input_rename_rank'] = 'Rename Rank: %s',
	['input_new_r_name'] = 'New Rank Name:',
	['input_confirm_r_name'] = 'Confirm New Rank Name:',
	['input_create_marker'] = 'Create Marker',
	['input_new_pin_code'] = 'New PIN-Code:',
	['input_confirm_pin_code'] = 'Confirm PIN-Code:',
} 
