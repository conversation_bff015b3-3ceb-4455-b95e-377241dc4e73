/**
 * @popperjs/core v2.4.0 - MIT License
 */

"use strict";!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).Popper={})}(this,(function(e){function t(e){return{width:(e=e.getBoundingClientRect()).width,height:e.height,top:e.top,right:e.right,bottom:e.bottom,left:e.left,x:e.left,y:e.top}}function n(e){return"[object Window]"!==e.toString()?(e=e.ownerDocument)?e.defaultView:window:e}function r(e){return{scrollLeft:(e=n(e)).pageXOffset,scrollTop:e.pageYOffset}}function o(e){return e instanceof n(e).Element||e instanceof Element}function i(e){return e instanceof n(e).HTMLElement||e instanceof HTMLElement}function a(e){return e?(e.nodeName||"").toLowerCase():null}function s(e){return(o(e)?e.ownerDocument:e.document).documentElement}function f(e){return t(s(e)).left+r(e).scrollLeft}function p(e){return n(e).getComputedStyle(e)}function c(e){return e=p(e),/auto|scroll|overlay|hidden/.test(e.overflow+e.overflowY+e.overflowX)}function u(e,o,p){void 0===p&&(p=!1);var u=s(o);e=t(e);var d={scrollLeft:0,scrollTop:0},l={x:0,y:0};return p||(("body"!==a(o)||c(u))&&(d=o!==n(o)&&i(o)?{scrollLeft:o.scrollLeft,scrollTop:o.scrollTop}:r(o)),i(o)?((l=t(o)).x+=o.clientLeft,l.y+=o.clientTop):u&&(l.x=f(u))),{x:e.left+d.scrollLeft-l.x,y:e.top+d.scrollTop-l.y,width:e.width,height:e.height}}function d(e){return{x:e.offsetLeft,y:e.offsetTop,width:e.offsetWidth,height:e.offsetHeight}}function l(e){return"html"===a(e)?e:e.assignedSlot||e.parentNode||e.host||s(e)}function m(e,t){void 0===t&&(t=[]);var r=function e(t){return 0<=["html","body","#document"].indexOf(a(t))?t.ownerDocument.body:i(t)&&c(t)?t:e(l(t))}(e);e="body"===a(r);var o=n(r);return r=e?[o].concat(o.visualViewport||[],c(r)?r:[]):r,t=t.concat(r),e?t:t.concat(m(l(r)))}function h(e){return i(e)&&"fixed"!==p(e).position?e.offsetParent:null}function v(e){var t=n(e);for(e=h(e);e&&0<=["table","td","th"].indexOf(a(e));)e=h(e);return e&&"body"===a(e)&&"static"===p(e).position?t:e||t}function g(e){var t=new Map,n=new Set,r=[];return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||function e(o){n.add(o.name),[].concat(o.requires||[],o.requiresIfExists||[]).forEach((function(r){n.has(r)||(r=t.get(r))&&e(r)})),r.push(o)}(e)})),r}function b(e){var t;return function(){return t||(t=new Promise((function(n){Promise.resolve().then((function(){t=void 0,n(e())}))}))),t}}function y(e){return e.split("-")[0]}function x(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function w(e){void 0===e&&(e={});var t=e.defaultModifiers,n=void 0===t?[]:t,r=void 0===(e=e.defaultOptions)?N:e;return function(e,t,i){function a(){f.forEach((function(e){return e()})),f=[]}void 0===i&&(i=r);var s={placement:"bottom",orderedModifiers:[],options:Object.assign({},N,{},r),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},f=[],p=!1,c={state:s,setOptions:function(i){return a(),s.options=Object.assign({},r,{},s.options,{},i),s.scrollParents={reference:o(e)?m(e):e.contextElement?m(e.contextElement):[],popper:m(t)},i=function(e){var t=g(e);return F.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}(function(e){var t=e.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,{},t,{options:Object.assign({},n.options,{},t.options),data:Object.assign({},n.data,{},t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}([].concat(n,s.options.modifiers))),s.orderedModifiers=i.filter((function(e){return e.enabled})),s.orderedModifiers.forEach((function(e){var t=e.name,n=e.options;n=void 0===n?{}:n,"function"==typeof(e=e.effect)&&(t=e({state:s,name:t,instance:c,options:n}),f.push(t||function(){}))})),c.update()},forceUpdate:function(){if(!p){var e=s.elements,t=e.reference;if(x(t,e=e.popper))for(s.rects={reference:u(t,v(e),"fixed"===s.options.strategy),popper:d(e)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach((function(e){return s.modifiersData[e.name]=Object.assign({},e.data)})),t=0;t<s.orderedModifiers.length;t++)if(!0===s.reset)s.reset=!1,t=-1;else{var n=s.orderedModifiers[t];e=n.fn;var r=n.options;r=void 0===r?{}:r,n=n.name,"function"==typeof e&&(s=e({state:s,options:r,name:n,instance:c})||s)}}},update:b((function(){return new Promise((function(e){c.forceUpdate(),e(s)}))})),destroy:function(){a(),p=!0}};return x(e,t)?(c.setOptions(i).then((function(e){!p&&i.onFirstUpdate&&i.onFirstUpdate(e)})),c):c}}function O(e){return 0<=["top","bottom"].indexOf(e)?"x":"y"}function M(e){var t=e.reference,n=e.element,r=(e=e.placement)?y(e):null;e=e?e.split("-")[1]:null;var o=t.x+t.width/2-n.width/2,i=t.y+t.height/2-n.height/2;switch(r){case"top":o={x:o,y:t.y-n.height};break;case"bottom":o={x:o,y:t.y+t.height};break;case"right":o={x:t.x+t.width,y:i};break;case"left":o={x:t.x-n.width,y:i};break;default:o={x:t.x,y:t.y}}if(null!=(r=r?O(r):null))switch(i="y"===r?"height":"width",e){case"start":o[r]=Math.floor(o[r])-Math.floor(t[i]/2-n[i]/2);break;case"end":o[r]=Math.floor(o[r])+Math.ceil(t[i]/2-n[i]/2)}return o}function j(e){var t,r=e.popper,o=e.popperRect,i=e.placement,a=e.offsets,f=e.position,p=e.gpuAcceleration,c=e.adaptive,u=window.devicePixelRatio||1;e=Math.round(a.x*u)/u||0,u=Math.round(a.y*u)/u||0;var d=a.hasOwnProperty("x");a=a.hasOwnProperty("y");var l,m="left",h="top",g=window;if(c){var b=v(r);b===n(r)&&(b=s(r)),"top"===i&&(h="bottom",u-=b.clientHeight-o.height,u*=p?1:-1),"left"===i&&(m="right",e-=b.clientWidth-o.width,e*=p?1:-1)}return r=Object.assign({position:f},c&&I),p?Object.assign({},r,((l={})[h]=a?"0":"",l[m]=d?"0":"",l.transform=2>(g.devicePixelRatio||1)?"translate("+e+"px, "+u+"px)":"translate3d("+e+"px, "+u+"px, 0)",l)):Object.assign({},r,((t={})[h]=a?u+"px":"",t[m]=d?e+"px":"",t.transform="",t))}function E(e){return e.replace(/left|right|bottom|top/g,(function(e){return _[e]}))}function D(e){return e.replace(/start|end/g,(function(e){return U[e]}))}function P(e,t){var n=!(!t.getRootNode||!t.getRootNode().host);if(e.contains(t))return!0;if(n)do{if(t&&e.isSameNode(t))return!0;t=t.parentNode||t.host}while(t);return!1}function L(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function k(e,o){if("viewport"===o){var a=n(e);e=a.visualViewport,o=a.innerWidth,a=a.innerHeight,e&&/iPhone|iPod|iPad/.test(navigator.platform)&&(o=e.width,a=e.height),e=L({width:o,height:a,x:0,y:0})}else i(o)?e=t(o):(e=n(a=s(e)),o=r(a),(a=u(s(a),e)).height=Math.max(a.height,e.innerHeight),a.width=Math.max(a.width,e.innerWidth),a.x=-o.scrollLeft,a.y=-o.scrollTop,e=L(a));return e}function B(e,t,r){return t="clippingParents"===t?function(e){var t=m(e),n=0<=["absolute","fixed"].indexOf(p(e).position)&&i(e)?v(e):e;return o(n)?t.filter((function(e){return o(e)&&P(e,n)})):[]}(e):[].concat(t),(r=(r=[].concat(t,[r])).reduce((function(t,r){var o=k(e,r),c=n(r=i(r)?r:s(e)),u=i(r)?p(r):{};parseFloat(u.borderTopWidth);var d=parseFloat(u.borderRightWidth)||0,l=parseFloat(u.borderBottomWidth)||0,m=parseFloat(u.borderLeftWidth)||0;u="html"===a(r);var h=f(r),v=r.clientWidth+d,g=r.clientHeight+l;return u&&50<c.innerHeight-r.clientHeight&&(g=c.innerHeight-l),l=u?0:r.clientTop,d=r.clientLeft>m?d:u?c.innerWidth-v-h:r.offsetWidth-v,c=u?c.innerHeight-g:r.offsetHeight-g,r=u?h:r.clientLeft,t.top=Math.max(o.top+l,t.top),t.right=Math.min(o.right-d,t.right),t.bottom=Math.min(o.bottom-c,t.bottom),t.left=Math.max(o.left+r,t.left),t}),k(e,r[0]))).width=r.right-r.left,r.height=r.bottom-r.top,r.x=r.left,r.y=r.top,r}function W(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},{},e)}function A(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function H(e,n){void 0===n&&(n={});var r=n;n=void 0===(n=r.placement)?e.placement:n;var i=r.boundary,a=void 0===i?"clippingParents":i,f=void 0===(i=r.rootBoundary)?"viewport":i;i=void 0===(i=r.elementContext)?"popper":i;var p=r.altBoundary,c=void 0!==p&&p;r=W("number"!=typeof(r=void 0===(r=r.padding)?0:r)?r:A(r,q));var u=e.elements.reference;p=e.rects.popper,a=B(o(c=e.elements[c?"popper"===i?"reference":"popper":i])?c:c.contextElement||s(e.elements.popper),a,f),c=M({reference:f=t(u),element:p,strategy:"absolute",placement:n}),p=L(Object.assign({},p,{},c)),f="popper"===i?p:f;var d={top:a.top-f.top+r.top,bottom:f.bottom-a.bottom+r.bottom,left:a.left-f.left+r.left,right:f.right-a.right+r.right};if(e=e.modifiersData.offset,"popper"===i&&e){var l=e[n];Object.keys(d).forEach((function(e){var t=0<=["right","bottom"].indexOf(e)?1:-1,n=0<=["top","bottom"].indexOf(e)?"y":"x";d[e]+=l[n]*t}))}return d}function T(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function R(e){return["top","right","bottom","left"].some((function(t){return 0<=e[t]}))}var q=["top","bottom","right","left"],S=q.reduce((function(e,t){return e.concat([t+"-start",t+"-end"])}),[]),C=[].concat(q,["auto"]).reduce((function(e,t){return e.concat([t,t+"-start",t+"-end"])}),[]),F="beforeRead read afterRead beforeMain main afterMain beforeWrite write afterWrite".split(" "),N={placement:"bottom",modifiers:[],strategy:"absolute"},V={passive:!0},I={top:"auto",right:"auto",bottom:"auto",left:"auto"},_={left:"right",right:"left",bottom:"top",top:"bottom"},U={start:"end",end:"start"},z=[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,r=e.instance,o=(e=e.options).scroll,i=void 0===o||o,a=void 0===(e=e.resize)||e,s=n(t.elements.popper),f=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&f.forEach((function(e){e.addEventListener("scroll",r.update,V)})),a&&s.addEventListener("resize",r.update,V),function(){i&&f.forEach((function(e){e.removeEventListener("scroll",r.update,V)})),a&&s.removeEventListener("resize",r.update,V)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state;t.modifiersData[e.name]=M({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options;e=void 0===(e=n.gpuAcceleration)||e,n=void 0===(n=n.adaptive)||n,e={placement:y(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:e},null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,{},j(Object.assign({},e,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:n})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,{},j(Object.assign({},e,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];i(o)&&a(o)&&(Object.assign(o.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],o=t.attributes[e]||{};e=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{}),i(r)&&a(r)&&(Object.assign(r.style,e),Object.keys(o).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.name,r=void 0===(e=e.options.offset)?[0,0]:e,o=(e=C.reduce((function(e,n){var o=t.rects,i=y(n),a=0<=["left","top"].indexOf(i)?-1:1,s="function"==typeof r?r(Object.assign({},o,{placement:n})):r;return o=(o=s[0])||0,s=((s=s[1])||0)*a,i=0<=["left","right"].indexOf(i)?{x:s,y:o}:{x:o,y:s},e[n]=i,e}),{}))[t.placement],i=o.x;o=o.y,null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=i,t.modifiersData.popperOffsets.y+=o),t.modifiersData[n]=e}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options;if(e=e.name,!t.modifiersData[e]._skip){var r=n.mainAxis;r=void 0===r||r;var o=n.altAxis;o=void 0===o||o;var i=n.fallbackPlacements,a=n.padding,s=n.boundary,f=n.rootBoundary,p=n.altBoundary,c=n.flipVariations,u=void 0===c||c,d=n.allowedAutoPlacements;c=y(n=t.options.placement),i=i||(c!==n&&u?function(e){if("auto"===y(e))return[];var t=E(e);return[D(e),t,D(t)]}(n):[E(n)]);var l=[n].concat(i).reduce((function(e,n){return e.concat("auto"===y(n)?function(e,t){void 0===t&&(t={});var n=t.boundary,r=t.rootBoundary,o=t.padding,i=t.flipVariations,a=t.allowedAutoPlacements,s=void 0===a?C:a,f=t.placement.split("-")[1],p=(f?i?S:S.filter((function(e){return e.split("-")[1]===f})):q).filter((function(e){return 0<=s.indexOf(e)})).reduce((function(t,i){return t[i]=H(e,{placement:i,boundary:n,rootBoundary:r,padding:o})[y(i)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}(t,{placement:n,boundary:s,rootBoundary:f,padding:a,flipVariations:u,allowedAutoPlacements:d}):n)}),[]);n=t.rects.reference,i=t.rects.popper;var m=new Map;c=!0;for(var h=l[0],v=0;v<l.length;v++){var g=l[v],b=y(g),x="start"===g.split("-")[1],w=0<=["top","bottom"].indexOf(b),O=w?"width":"height",M=H(t,{placement:g,boundary:s,rootBoundary:f,altBoundary:p,padding:a});if(x=w?x?"right":"left":x?"bottom":"top",n[O]>i[O]&&(x=E(x)),O=E(x),w=[],r&&w.push(0>=M[b]),o&&w.push(0>=M[x],0>=M[O]),w.every((function(e){return e}))){h=g,c=!1;break}m.set(g,w)}if(c)for(r=function(e){var t=l.find((function(t){if(t=m.get(t))return t.slice(0,e).every((function(e){return e}))}));if(t)return h=t,"break"},o=u?3:1;0<o&&"break"!==r(o);o--);t.placement!==h&&(t.modifiersData[e]._skip=!0,t.placement=h,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options;e=e.name;var r=n.mainAxis,o=void 0===r||r;r=void 0!==(r=n.altAxis)&&r;var i=n.tether;i=void 0===i||i;var a=n.tetherOffset,s=void 0===a?0:a;n=H(t,{boundary:n.boundary,rootBoundary:n.rootBoundary,padding:n.padding,altBoundary:n.altBoundary}),a=y(t.placement);var f=t.placement.split("-")[1],p=!f,c=O(a);a="x"===c?"y":"x";var u=t.modifiersData.popperOffsets,l=t.rects.reference,m=t.rects.popper,h="function"==typeof s?s(Object.assign({},t.rects,{placement:t.placement})):s;if(s={x:0,y:0},u){if(o){var g="y"===c?"top":"left",b="y"===c?"bottom":"right",x="y"===c?"height":"width";o=u[c];var w=u[c]+n[g],M=u[c]-n[b],j=i?-m[x]/2:0,E="start"===f?l[x]:m[x];f="start"===f?-m[x]:-l[x],m=t.elements.arrow,m=i&&m?d(m):{width:0,height:0};var D=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0};g=D[g],b=D[b],m=Math.max(0,Math.min(l[x],m[x])),E=p?l[x]/2-j-m-g-h:E-m-g-h,p=p?-l[x]/2+j+m+b+h:f+m+b+h,h=t.elements.arrow&&v(t.elements.arrow),l=t.modifiersData.offset?t.modifiersData.offset[t.placement][c]:0,h=u[c]+E-l-(h?"y"===c?h.clientTop||0:h.clientLeft||0:0),p=u[c]+p-l,i=Math.max(i?Math.min(w,h):w,Math.min(o,i?Math.max(M,p):M)),u[c]=i,s[c]=i-o}r&&(r=u[a],i=Math.max(r+n["x"===c?"top":"left"],Math.min(r,r-n["x"===c?"bottom":"right"])),u[a]=i,s[a]=i-r),t.modifiersData[e]=s}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state;e=e.name;var r=n.elements.arrow,o=n.modifiersData.popperOffsets,i=y(n.placement),a=O(i);if(i=0<=["left","right"].indexOf(i)?"height":"width",r&&o){var s=n.modifiersData[e+"#persistent"].padding,f=d(r),p="y"===a?"top":"left",c="y"===a?"bottom":"right",u=n.rects.reference[i]+n.rects.reference[a]-o[a]-n.rects.popper[i];o=o[a]-n.rects.reference[a],u=(r=(r=v(r))?"y"===a?r.clientHeight||0:r.clientWidth||0:0)/2-f[i]/2+(u/2-o/2),i=Math.max(s[p],Math.min(u,r-f[i]-s[c])),n.modifiersData[e]=((t={})[a]=i,t.centerOffset=i-u,t)}},effect:function(e){var t=e.state,n=e.options;e=e.name;var r=n.element;if(r=void 0===r?"[data-popper-arrow]":r,n=void 0===(n=n.padding)?0:n,null!=r){if("string"==typeof r&&!(r=t.elements.popper.querySelector(r)))return;P(t.elements.popper,r)&&(t.elements.arrow=r,t.modifiersData[e+"#persistent"]={padding:W("number"!=typeof n?n:A(n,q))})}},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state;e=e.name;var n=t.rects.reference,r=t.rects.popper,o=t.modifiersData.preventOverflow,i=H(t,{elementContext:"reference"}),a=H(t,{altBoundary:!0});n=T(i,n),r=T(a,r,o),o=R(n),a=R(r),t.modifiersData[e]={referenceClippingOffsets:n,popperEscapeOffsets:r,isReferenceHidden:o,hasPopperEscaped:a},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":o,"data-popper-escaped":a})}}],X=w({defaultModifiers:z});e.createPopper=X,e.defaultModifiers=z,e.detectOverflow=H,e.popperGenerator=w,Object.defineProperty(e,"__esModule",{value:!0})}));

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@popperjs/core")):"function"==typeof define&&define.amd?define(["@popperjs/core"],e):(t=t||self).tippy=e(t.Popper)}(this,(function(t){"use strict";var e="undefined"!=typeof window&&"undefined"!=typeof document,n=e?navigator.userAgent:"",i=/MSIE |Trident\//.test(n),r={passive:!0,capture:!0};function o(t,e,n){if(Array.isArray(t)){var i=t[e];return null==i?Array.isArray(n)?n[e]:n:i}return t}function a(t,e){var n={}.toString.call(t);return 0===n.indexOf("[object")&&n.indexOf(e+"]")>-1}function s(t,e){return"function"==typeof t?t.apply(void 0,e):t}function p(t,e){return 0===e?t:function(i){clearTimeout(n),n=setTimeout((function(){t(i)}),e)};var n}function u(t,e){var n=Object.assign({},t);return e.forEach((function(t){delete n[t]})),n}function c(t){return[].concat(t)}function f(t,e){-1===t.indexOf(e)&&t.push(e)}function l(t){return t.split("-")[0]}function d(t){return[].slice.call(t)}function v(){return document.createElement("div")}function m(t){return["Element","Fragment"].some((function(e){return a(t,e)}))}function g(t){return a(t,"MouseEvent")}function h(t){return!(!t||!t._tippy||t._tippy.reference!==t)}function b(t){return m(t)?[t]:function(t){return a(t,"NodeList")}(t)?d(t):Array.isArray(t)?t:d(document.querySelectorAll(t))}function y(t,e){t.forEach((function(t){t&&(t.style.transitionDuration=e+"ms")}))}function x(t,e){t.forEach((function(t){t&&t.setAttribute("data-state",e)}))}function w(t){var e=c(t)[0];return e&&e.ownerDocument||document}function E(t,e,n){var i=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(e){t[i](e,n)}))}var T={isTouch:!1},A=0;function C(){T.isTouch||(T.isTouch=!0,window.performance&&document.addEventListener("mousemove",O))}function O(){var t=performance.now();t-A<20&&(T.isTouch=!1,document.removeEventListener("mousemove",O)),A=t}function L(){var t=document.activeElement;if(h(t)){var e=t._tippy;t.blur&&!e.state.isVisible&&t.blur()}}var D=Object.assign({appendTo:function(){return document.body},aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),k=Object.keys(D);function M(t){var e=(t.plugins||[]).reduce((function(e,n){var i=n.name,r=n.defaultValue;return i&&(e[i]=void 0!==t[i]?t[i]:r),e}),{});return Object.assign({},t,{},e)}function V(t,e){var n=Object.assign({},e,{content:s(e.content,[t])},e.ignoreAttributes?{}:function(t,e){return(e?Object.keys(M(Object.assign({},D,{plugins:e}))):k).reduce((function(e,n){var i=(t.getAttribute("data-tippy-"+n)||"").trim();if(!i)return e;if("content"===n)e[n]=i;else try{e[n]=JSON.parse(i)}catch(t){e[n]=i}return e}),{})}(t,e.plugins));return n.aria=Object.assign({},D.aria,{},n.aria),n.aria={expanded:"auto"===n.aria.expanded?e.interactive:n.aria.expanded,content:"auto"===n.aria.content?e.interactive?null:"describedby":n.aria.content},n}function R(t,e){t.innerHTML=e}function j(t){var e=v();return!0===t?e.className="tippy-arrow":(e.className="tippy-svg-arrow",m(t)?e.appendChild(t):R(e,t)),e}function P(t,e){m(e.content)?(R(t,""),t.appendChild(e.content)):"function"!=typeof e.content&&(e.allowHTML?R(t,e.content):t.textContent=e.content)}function I(t){var e=t.firstElementChild,n=d(e.children);return{box:e,content:n.find((function(t){return t.classList.contains("tippy-content")})),arrow:n.find((function(t){return t.classList.contains("tippy-arrow")||t.classList.contains("tippy-svg-arrow")})),backdrop:n.find((function(t){return t.classList.contains("tippy-backdrop")}))}}function S(t){var e=v(),n=v();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var i=v();function r(n,i){var r=I(e),o=r.box,a=r.content,s=r.arrow;i.theme?o.setAttribute("data-theme",i.theme):o.removeAttribute("data-theme"),"string"==typeof i.animation?o.setAttribute("data-animation",i.animation):o.removeAttribute("data-animation"),i.inertia?o.setAttribute("data-inertia",""):o.removeAttribute("data-inertia"),o.style.maxWidth="number"==typeof i.maxWidth?i.maxWidth+"px":i.maxWidth,i.role?o.setAttribute("role",i.role):o.removeAttribute("role"),n.content===i.content&&n.allowHTML===i.allowHTML||P(a,t.props),i.arrow?s?n.arrow!==i.arrow&&(o.removeChild(s),o.appendChild(j(i.arrow))):o.appendChild(j(i.arrow)):s&&o.removeChild(s)}return i.className="tippy-content",i.setAttribute("data-state","hidden"),P(i,t.props),e.appendChild(n),n.appendChild(i),r(t.props,t.props),{popper:e,onUpdate:r}}S.$$tippy=!0;var B=1,H=[],U=[];function N(e,n){var a,u,m,h,b,A,C,O,L=V(e,Object.assign({},D,{},M(n))),k=!1,R=!1,j=!1,P=!1,S=[],N=p(ht,L.interactiveDebounce),X=w(L.triggerTarget||e),Y=B++,_=(O=L.plugins).filter((function(t,e){return O.indexOf(t)===e})),z={id:Y,reference:e,popper:v(),popperInstance:null,props:L,state:{isEnabled:!0,isVisible:!1,isDestroyedByDurability:!1,isMounted:!1,isShown:!1},plugins:_,clearDelayTimeouts:function(){clearTimeout(a),clearTimeout(u),cancelAnimationFrame(m)},setProps:function(t){if(z.state.isDestroyedByDurability)return;it("onBeforeUpdate",[z,t]),mt();var n=z.props,i=V(e,Object.assign({},z.props,{},t,{ignoreAttributes:!0}));z.props=i,vt(),n.interactiveDebounce!==i.interactiveDebounce&&(at(),N=p(ht,i.interactiveDebounce));n.triggerTarget&&!i.triggerTarget?c(n.triggerTarget).forEach((function(t){t.removeAttribute("aria-expanded")})):i.triggerTarget&&e.removeAttribute("aria-expanded");ot(),nt(),q&&q(n,i);z.popperInstance&&(wt(),Tt().forEach((function(t){requestAnimationFrame(t._tippy.popperInstance.forceUpdate)})));it("onAfterUpdate",[z,t])},setContent:function(t){z.setProps({content:t})},show:function(){var t=z.state.isVisible,e=z.state.isDestroyedByDurability,n=!z.state.isEnabled,i=T.isTouch&&!z.props.touch,r=o(z.props.duration,0,D.duration);if(t||e||n||i)return;if(Z().hasAttribute("disabled"))return;if(it("onShow",[z],!1),!1===z.props.onShow(z))return;z.state.isVisible=!0,Q()&&(W.style.visibility="visible");nt(),ct(),z.state.isMounted||(W.style.transition="none");if(Q()){var a=tt(),p=a.box,u=a.content;y([p,u],0)}A=function(){if(z.state.isVisible&&!P){if(P=!0,W.offsetHeight,W.style.transition=z.props.moveTransition,Q()&&z.props.animation){var t=tt(),e=t.box,n=t.content;y([e,n],r),x([e,n],"visible")}rt(),ot(),f(U,z),z.state.isMounted=!0,it("onMount",[z]),z.props.animation&&Q()&&function(t,e){lt(t,e)}(r,(function(){z.state.isShown=!0,it("onShown",[z])}))}},function(){var t,e=z.props.appendTo,n=Z();t=z.props.interactive&&e===D.appendTo||"parent"===e?n.parentNode:s(e,[n]);t.contains(W)||t.appendChild(W);wt()}()},hide:function(){var t=!z.state.isVisible,e=z.state.isDestroyedByDurability,n=!z.state.isEnabled,i=o(z.props.duration,1,D.duration);if(t||e||n)return;if(it("onHide",[z],!1),!1===z.props.onHide(z))return;z.state.isVisible=!1,z.state.isShown=!1,P=!1,Q()&&(W.style.visibility="hidden");if(at(),ft(),nt(),Q()){var r=tt(),a=r.box,s=r.content;z.props.animation&&(y([a,s],i),x([a,s],"hidden"))}rt(),ot(),z.props.animation?Q()&&function(t,e){lt(t,(function(){!z.state.isVisible&&W.parentNode&&W.parentNode.contains(W)&&e()}))}(i,z.unmount):z.unmount()},hideWithInteractivity:function(t){X.body.addEventListener("mouseleave",Ct),X.addEventListener("mousemove",N),f(H,N),N(t)},enable:function(){z.state.isEnabled=!0},disable:function(){z.hide(),z.state.isEnabled=!1},unmount:function(){z.state.isVisible&&z.hide();if(!z.state.isMounted)return;Et(),Tt().forEach((function(t){t._tippy.unmount()})),W.parentNode&&W.parentNode.removeChild(W);U=U.filter((function(t){return t!==z})),z.state.isMounted=!1,it("onHidden",[z])},destroy:function(){if(z.state.isDestroyedByDurability)return;z.clearDelayTimeouts(),z.unmount(),mt(),delete e._tippy,z.state.isDestroyedByDurability=!0,it("onDestroy",[z])}};if(!L.render)return z;var F=L.render(z),W=F.popper,q=F.onUpdate;W.setAttribute("data-tippy-root",""),W.id="tippy-"+z.id,z.popper=W,e._tippy=z,W._tippy=z;var $=_.map((function(t){return t.fn(z)})),J=e.hasAttribute("aria-expanded");return vt(),ot(),nt(),it("onCreate",[z]),L.showOnCreate&&At(),W.addEventListener("mouseenter",(function(){z.props.interactive&&z.state.isVisible&&z.clearDelayTimeouts()})),W.addEventListener("mouseleave",(function(t){z.props.interactive&&z.props.trigger.indexOf("mouseenter")>=0&&(X.addEventListener("mousemove",N),N(t))})),z;function G(){var t=z.props.touch;return Array.isArray(t)?t:[t,0]}function K(){return"hold"===G()[0]}function Q(){var t;return!!(null==(t=z.props.render)?void 0:t.$$tippy)}function Z(){return C||e}function tt(){return I(W)}function et(t){return z.state.isMounted&&!z.state.isVisible||T.isTouch||h&&"focus"===h.type?0:o(z.props.delay,t?0:1,D.delay)}function nt(){W.style.pointerEvents=z.props.interactive&&z.state.isVisible?"":"none",W.style.zIndex=""+z.props.zIndex}function it(t,e,n){var i;(void 0===n&&(n=!0),$.forEach((function(n){n[t]&&n[t].apply(void 0,e)})),n)&&(i=z.props)[t].apply(i,e)}function rt(){var t=z.props.aria;if(t.content){var n="aria-"+t.content,i=W.id;c(z.props.triggerTarget||e).forEach((function(t){var e=t.getAttribute(n);if(z.state.isVisible)t.setAttribute(n,e?e+" "+i:i);else{var r=e&&e.replace(i,"").trim();r?t.setAttribute(n,r):t.removeAttribute(n)}}))}}function ot(){!J&&z.props.aria.expanded&&c(z.props.triggerTarget||e).forEach((function(t){z.props.interactive?t.setAttribute("aria-expanded",z.state.isVisible&&t===Z()?"true":"false"):t.removeAttribute("aria-expanded")}))}function at(){X.body.removeEventListener("mouseleave",Ct),X.removeEventListener("mousemove",N),H=H.filter((function(t){return t!==N}))}function st(t){if(!(T.isTouch&&(j||"mousedown"===t.type)||z.props.interactive&&W.contains(t.target))){if(Z().contains(t.target)){if(T.isTouch)return;if(z.state.isVisible&&z.props.trigger.indexOf("click")>=0)return}else it("onClickOutside",[z,t]);!0===z.props.hideOnClick&&(k=!1,z.clearDelayTimeouts(),z.hide(),R=!0,setTimeout((function(){R=!1})),z.state.isMounted||ft())}}function pt(){j=!0}function ut(){j=!1}function ct(){X.addEventListener("mousedown",st,!0),X.addEventListener("touchend",st,r),X.addEventListener("touchstart",ut,r),X.addEventListener("touchmove",pt,r)}function ft(){X.removeEventListener("mousedown",st,!0),X.removeEventListener("touchend",st,r),X.removeEventListener("touchstart",ut,r),X.removeEventListener("touchmove",pt,r)}function lt(t,e){var n=tt().box;function i(t){t.target===n&&(E(n,"remove",i),e())}if(0===t)return e();E(n,"remove",b),E(n,"add",i),b=i}function dt(t,n,i){void 0===i&&(i=!1),c(z.props.triggerTarget||e).forEach((function(e){e.addEventListener(t,n,i),S.push({node:e,eventType:t,handler:n,options:i})}))}function vt(){var t;K()&&(dt("touchstart",gt,{passive:!0}),dt("touchend",bt,{passive:!0})),(t=z.props.trigger,t.split(/\s+/).filter(Boolean)).forEach((function(t){if("manual"!==t)switch(dt(t,gt),t){case"mouseenter":dt("mouseleave",bt);break;case"focus":dt(i?"focusout":"blur",yt);break;case"focusin":dt("focusout",yt)}}))}function mt(){S.forEach((function(t){var e=t.node,n=t.eventType,i=t.handler,r=t.options;e.removeEventListener(n,i,r)})),S=[]}function gt(t){var e,n=!1;if(z.state.isEnabled&&!xt(t)&&!R){var i="focus"===(null==(e=h)?void 0:e.type);h=t,C=t.currentTarget,ot(),!z.state.isVisible&&g(t)&&H.forEach((function(e){return e(t)})),"click"===t.type&&(z.props.trigger.indexOf("mouseenter")<0||k)&&!1!==z.props.hideOnClick&&z.state.isVisible?n=!0:At(t),"click"===t.type&&(k=!n),n&&!i&&Ct(t)}}function ht(t){var n=t.target,i=e.contains(n)||W.contains(n);"mousemove"===t.type&&i||function(t,e){var n=e.clientX,i=e.clientY;return t.every((function(t){var e=t.popperRect,r=t.popperState,o=t.props.interactiveBorder,a=l(r.placement),s=r.modifiersData.offset;if(!s)return!0;var p="bottom"===a?s.top.y:0,u="top"===a?s.bottom.y:0,c="right"===a?s.left.x:0,f="left"===a?s.right.x:0,d=e.top-i+p>o,v=i-e.bottom-u>o,m=e.left-n+c>o,g=n-e.right-f>o;return d||v||m||g}))}(Tt().concat(W).map((function(t){var e,n=null==(e=t._tippy.popperInstance)?void 0:e.state;return n?{popperRect:t.getBoundingClientRect(),popperState:n,props:L}:null})).filter(Boolean),t)&&(at(),Ct(t))}function bt(t){xt(t)||z.props.trigger.indexOf("click")>=0&&k||(z.props.interactive?z.hideWithInteractivity(t):Ct(t))}function yt(t){z.props.trigger.indexOf("focusin")<0&&t.target!==Z()||z.props.interactive&&t.relatedTarget&&W.contains(t.relatedTarget)||Ct(t)}function xt(t){return!!T.isTouch&&K()!==t.type.indexOf("touch")>=0}function wt(){Et();var n=z.props,i=n.popperOptions,r=n.placement,o=n.offset,a=n.getReferenceClientRect,s=n.moveTransition,p=Q()?I(W).arrow:null,u=a?{getBoundingClientRect:a,contextElement:a.contextElement||Z()}:e,c=[{name:"offset",options:{offset:o}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!s}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(t){var e=t.state;if(Q()){var n=tt().box;["placement","reference-hidden","escaped"].forEach((function(t){"placement"===t?n.setAttribute("data-placement",e.placement):e.attributes.popper["data-popper-"+t]?n.setAttribute("data-"+t,""):n.removeAttribute("data-"+t)})),e.attributes.popper={}}}}];Q()&&p&&c.push({name:"arrow",options:{element:p,padding:3}}),c.push.apply(c,(null==i?void 0:i.modifiers)||[]),z.popperInstance=t.createPopper(u,W,Object.assign({},i,{placement:r,onFirstUpdate:A,modifiers:c}))}function Et(){z.popperInstance&&(z.popperInstance.destroy(),z.popperInstance=null)}function Tt(){return d(W.querySelectorAll("[data-tippy-root]"))}function At(t){z.clearDelayTimeouts(),t&&it("onTrigger",[z,t]),ct();var e=et(!0),n=G(),i=n[0],r=n[1];T.isTouch&&"hold"===i&&r&&(e=r),e?a=setTimeout((function(){z.show()}),e):z.show()}function Ct(t){if(z.clearDelayTimeouts(),it("onUntrigger",[z,t]),z.state.isVisible){if(!(z.props.trigger.indexOf("mouseenter")>=0&&z.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0&&k)){var e=et(!1);e?u=setTimeout((function(){z.state.isVisible&&z.hide()}),e):m=requestAnimationFrame((function(){z.hide()}))}}else ft()}}function X(t,e){void 0===e&&(e={});var n=D.plugins.concat(e.plugins||[]);document.addEventListener("touchstart",C,r),window.addEventListener("blur",L);var i=Object.assign({},e,{plugins:n}),o=b(t).reduce((function(t,e){var n=e&&N(e,i);return n&&t.push(n),t}),[]);return m(t)?o[0]:o}X.defaultProps=D,X.setDefaultProps=function(t){Object.keys(t).forEach((function(e){D[e]=t[e]}))},X.currentInput=T;var Y={mouseover:"mouseenter",focusin:"focus",click:"click"};var _={name:"animateFill",defaultValue:!1,fn:function(t){var e;if(!(null==(e=t.props.render)?void 0:e.$$tippy))return{};var n=I(t.popper),i=n.box,r=n.content,o=t.props.animateFill?function(){var t=v();return t.className="tippy-backdrop",x([t],"hidden"),t}():null;return{onCreate:function(){o&&(i.insertBefore(o,i.firstElementChild),i.setAttribute("data-animatefill",""),i.style.overflow="hidden",t.setProps({arrow:!1,animation:"shift-away"}))},onMount:function(){if(o){var t=i.style.transitionDuration,e=Number(t.replace("ms",""));r.style.transitionDelay=Math.round(e/10)+"ms",o.style.transitionDuration=t,x([o],"visible")}},onShow:function(){o&&(o.style.transitionDuration="0ms")},onHide:function(){o&&x([o],"hidden")}}}};var z={name:"followCursor",defaultValue:!1,fn:function(t){var e=t.reference,n=w(t.props.triggerTarget||e),i=null;function r(){return"manual"===t.props.trigger.trim()}function o(){var e=!!r()||null!==i&&!(0===i.clientX&&0===i.clientY);return t.props.followCursor&&e}function a(e){e&&t.setProps({getReferenceClientRect:null})}function s(){o()?n.addEventListener("mousemove",u):a(t.props.followCursor)}function p(){n.removeEventListener("mousemove",u)}function u(n){i={clientX:n.clientX,clientY:n.clientY};var r=!n.target||e.contains(n.target),o=t.props.followCursor,a=n.clientX,s=n.clientY,u=e.getBoundingClientRect(),c=a-u.left,f=s-u.top;!r&&t.props.interactive||t.setProps({getReferenceClientRect:function(){var t=e.getBoundingClientRect(),n=a,i=s;"initial"===o&&(n=t.left+c,i=t.top+f);var r="horizontal"===o?t.top:i,p="vertical"===o?t.right:n,u="horizontal"===o?t.bottom:i,l="vertical"===o?t.left:n;return{width:p-l,height:u-r,top:r,right:p,bottom:u,left:l}}}),(T.isTouch||"initial"===t.props.followCursor&&t.state.isVisible)&&p()}return{onAfterUpdate:function(t,e){var n=e.followCursor;void 0===n||n||a(!0)},onMount:function(){o()&&u(i)},onShow:function(){r()&&(i={clientX:0,clientY:0},s())},onTrigger:function(t,e){i||(g(e)&&(i={clientX:e.clientX,clientY:e.clientY}),s())},onUntrigger:function(){t.state.isVisible||(p(),i=null)},onHidden:function(){p(),i=null}}}};var F={name:"inlinePositioning",defaultValue:!1,fn:function(t){var e,n=t.reference;var i=-1,r=!1,o={name:"tippyInlinePositioning",enabled:!0,phase:"afterWrite",fn:function(r){var o=r.state;t.props.inlinePositioning&&(e!==o.placement&&t.setProps({getReferenceClientRect:function(){return function(t){return function(t,e,n,i){if(n.length<2||null===t)return e;if(2===n.length&&i>=0&&n[0].left>n[1].right)return n[i]||e;switch(t){case"top":case"bottom":var r=n[0],o=n[n.length-1],a="top"===t,s=r.top,p=o.bottom,u=a?r.left:o.left,c=a?r.right:o.right;return{top:s,bottom:p,left:u,right:c,width:c-u,height:p-s};case"left":case"right":var f=Math.min.apply(Math,n.map((function(t){return t.left}))),l=Math.max.apply(Math,n.map((function(t){return t.right}))),d=n.filter((function(e){return"left"===t?e.left===f:e.right===l})),v=d[0].top,m=d[d.length-1].bottom;return{top:v,bottom:m,left:f,right:l,width:l-f,height:m-v};default:return e}}(l(t),n.getBoundingClientRect(),d(n.getClientRects()),i)}(o.placement)}}),e=o.placement)}};function a(){var e;r||(e=function(t,e){var n;return{popperOptions:Object.assign({},t.popperOptions,{modifiers:[].concat(((null==(n=t.popperOptions)?void 0:n.modifiers)||[]).filter((function(t){return t.name!==e.name})),[e])})}}(t.props,o),r=!0,t.setProps(e),r=!1)}return{onCreate:a,onAfterUpdate:a,onTrigger:function(e,n){if(g(n)){var r=d(t.reference.getClientRects()),o=r.find((function(t){return t.left-2<=n.clientX&&t.right+2>=n.clientX&&t.top-2<=n.clientY&&t.bottom+2>=n.clientY}));i=r.indexOf(o)}},onUntrigger:function(){i=-1}}}};var W={name:"sticky",defaultValue:!1,fn:function(t){var e=t.reference,n=t.popper;function i(e){return!0===t.props.sticky||t.props.sticky===e}var r=null,o=null;function a(){var s=i("reference")?(t.popperInstance?t.popperInstance.state.elements.reference:e).getBoundingClientRect():null,p=i("popper")?n.getBoundingClientRect():null;(s&&q(r,s)||p&&q(o,p))&&t.popperInstance&&t.popperInstance.update(),r=s,o=p,t.state.isMounted&&requestAnimationFrame(a)}return{onMount:function(){t.props.sticky&&a()}}}};function q(t,e){return!t||!e||(t.top!==e.top||t.right!==e.right||t.bottom!==e.bottom||t.left!==e.left)}return e&&function(t){var e=document.createElement("style");e.textContent=t,e.setAttribute("data-tippy-stylesheet","");var n=document.head,i=document.querySelector("head>style,head>link");i?n.insertBefore(e,i):n.appendChild(e)}('.tippy-box[data-animation=fade][data-state=hidden]{opacity:0}[data-tippy-root]{max-width:calc(100vw - 10px)}.tippy-box{position:relative;background-color:#333;color:#fff;border-radius:4px;font-size:14px;line-height:1.4;outline:0;transition-property:transform,visibility,opacity}.tippy-box[data-placement^=top]>.tippy-arrow{bottom:0}.tippy-box[data-placement^=top]>.tippy-arrow:before{bottom:-7px;left:0;border-width:8px 8px 0;border-top-color:initial;transform-origin:center top}.tippy-box[data-placement^=bottom]>.tippy-arrow{top:0}.tippy-box[data-placement^=bottom]>.tippy-arrow:before{top:-7px;left:0;border-width:0 8px 8px;border-bottom-color:initial;transform-origin:center bottom}.tippy-box[data-placement^=left]>.tippy-arrow{right:0}.tippy-box[data-placement^=left]>.tippy-arrow:before{border-width:8px 0 8px 8px;border-left-color:initial;right:-7px;transform-origin:center left}.tippy-box[data-placement^=right]>.tippy-arrow{left:0}.tippy-box[data-placement^=right]>.tippy-arrow:before{left:-7px;border-width:8px 8px 8px 0;border-right-color:initial;transform-origin:center right}.tippy-box[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-arrow{width:16px;height:16px;color:#333}.tippy-arrow:before{content:"";position:absolute;border-color:transparent;border-style:solid}.tippy-content{position:relative;padding:5px 9px;z-index:1}'),X.setDefaultProps({plugins:[_,z,F,W],render:S}),X.createSingleton=function(t,e){void 0===e&&(e={});var n,i=t,r=[],o=e.overrides;function a(){r=i.map((function(t){return t.reference}))}function s(t){i.forEach((function(e){t?e.enable():e.disable()}))}s(!1),a();var p={fn:function(){return{onDestroy:function(){s(!0)},onTrigger:function(t,e){var a=e.currentTarget,s=r.indexOf(a);if(a!==n){n=a;var p=(o||[]).concat("content").reduce((function(t,e){return t[e]=i[s].props[e],t}),{});t.setProps(Object.assign({},p,{getReferenceClientRect:function(){return a.getBoundingClientRect()}}))}}}}},c=X(v(),Object.assign({},u(e,["overrides"]),{plugins:[p].concat(e.plugins||[]),triggerTarget:r})),f=c.setProps;return c.setProps=function(t){o=t.overrides||o,f(t)},c.setInstances=function(t){s(!0),i=t,s(!1),a(),c.setProps({triggerTarget:r})},c},X.delegate=function(t,e){var n=[],i=[],r=e.target,o=u(e,["target"]),a=Object.assign({},o,{trigger:"manual",touch:!1}),s=Object.assign({},o,{showOnCreate:!0}),p=X(t,a);function f(t){if(t.target){var n=t.target.closest(r);if(n){var o=n.getAttribute("data-tippy-trigger")||e.trigger||D.trigger;if(!n._tippy&&!("touchstart"===t.type&&"boolean"==typeof s.touch||"touchstart"!==t.type&&o.indexOf(Y[t.type]))){var a=X(n,s);a&&(i=i.concat(a))}}}}function l(t,e,i,r){void 0===r&&(r=!1),t.addEventListener(e,i,r),n.push({node:t,eventType:e,handler:i,options:r})}return c(p).forEach((function(t){var e=t.destroy;t.destroy=function(t){void 0===t&&(t=!0),t&&i.forEach((function(t){t.destroy()})),i=[],n.forEach((function(t){var e=t.node,n=t.eventType,i=t.handler,r=t.options;e.removeEventListener(n,i,r)})),n=[],e()},function(t){var e=t.reference;l(e,"touchstart",f),l(e,"mouseover",f),l(e,"focusin",f),l(e,"click",f)}(t)})),p},X.hideAll=function(t){var e=void 0===t?{}:t,n=e.exclude,i=e.duration;U.forEach((function(t){var e=!1;if(n&&(e=h(n)?t.reference===n:t.popper===n.popper),!e){var r=t.props.duration;t.setProps({duration:i}),t.hide(),t.state.isDestroyedByDurability||t.setProps({duration:r})}}))},X.roundArrow='<svg width="16" height="6" xmlns="http://www.w3.org/2000/svg"><path d="M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z"></svg>',X}));
