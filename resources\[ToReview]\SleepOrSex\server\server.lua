ESX = nil

TriggerEvent(Config.TriggerEvent, function(obj) ESX = obj end)

RegisterServerEvent('podol:starteweyan')
AddEventHandler('podol:starteweyan', function(target)
	local targetPlayer = ESX.GetPlayerFromId(target)

	TriggerClientEvent('podol:gayaeweyan', targetPlayer.source, source)
	TriggerClientEvent('podol:gayaeweyan2', source)
end)

RegisterServerEvent('podol:starteweyankasur')
AddEventHandler('podol:starteweyankasur', function(target)
	local targetPlayer = ESX.GetPlayerFromId(target)

	TriggerClientEvent('podol:gayakasur', targetPlayer.source, source)
	TriggerClientEvent('podol:gayakasur2', source)
end)

RegisterServerEvent('podol:condomuse')
AddEventHandler('podol:condomuse', function(target)
	--local targetPlayer = ESX.GetPlayerFromId(target)

	TriggerClientEvent('podol:condom2', target)
	TriggerClientEvent('podol:condom', source)
end)

RegisterServerEvent('podol:testpackuse')
AddEventHandler('podol:testpackuse', function(target)
	--local targetPlayer = ESX.GetPlayerFromId(target)

	TriggerClientEvent('podol:testpack2', target)
	TriggerClientEvent('podol:testpack', source)
end)

RegisterServerEvent('podol:sleep')
AddEventHandler('podol:sleep', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	TriggerClientEvent('esx_status:remove', source, 'thirst', Config.Thirst)
	TriggerClientEvent('esx_status:remove', source, 'hunger', Config.Hunger)
	--TriggerClientEvent('esx_status:add', source, 'kalori', 10000)
	TriggerClientEvent('esx:showNotification', source, _U('wakeup'))	
end)	

RegisterServerEvent('podol:refundcondom')
AddEventHandler('podol:refundcondom', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.addInventoryItem('condom', 1)
end)	

RegisterServerEvent('podol:refundtestpack')
AddEventHandler('podol:refundtestpack', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.addInventoryItem('testpack', 1)
end)	

RegisterServerEvent('podol:refundviagra')
AddEventHandler('podol:refundviagra', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.addInventoryItem(Config.ItemViagra, 1)
end)	

if Config.ItemViagra then
	ESX.RegisterUsableItem(Config.ItemViagra, function(source)
		local xPlayer = ESX.GetPlayerFromId(source)
		local viagra = xPlayer.getInventoryItem(Config.ItemViagra).count
		if viagra > 0 then
		xPlayer.removeInventoryItem(Config.ItemViagra, 1)
		TriggerClientEvent('podol:viagrasync', source)
		end
	end)
end

ESX.RegisterUsableItem('condom', function(source)
    local xPlayer = ESX.GetPlayerFromId(source)
	local condom = xPlayer.getInventoryItem('condom').count
	if condom > 0 then
	xPlayer.removeInventoryItem('condom', 1)
	TriggerClientEvent('podol:condomsync', source)
	end
end)

ESX.RegisterUsableItem('testpack', function(source)
    local xPlayer = ESX.GetPlayerFromId(source)
	local testpack = xPlayer.getInventoryItem('testpack').count
	if testpack > 0 then
	xPlayer.removeInventoryItem('testpack', 1)
	TriggerClientEvent('podol:testpacksync', source)
	end
end)

ESX.RegisterUsableItem('levonorgestrel', function(source)
    local xPlayer = ESX.GetPlayerFromId(source)
	local levonorgestrel = xPlayer.getInventoryItem('levonorgestrel').count
	if levonorgestrel > 0 then
	xPlayer.removeInventoryItem('levonorgestrel', 1)
    TriggerClientEvent('podol:levonorgestrel', source)
	TriggerClientEvent('esx:showNotification', source, _U('levonorgestrel'))
	end
end)

ESX.RegisterUsableItem('mifepristone', function(source)
    local xPlayer = ESX.GetPlayerFromId(source)
	local mifepristone = xPlayer.getInventoryItem('mifepristone').count
	if mifepristone > 0 then
	xPlayer.removeInventoryItem('mifepristone', 1)
    TriggerClientEvent('podol:abortion', source)
	TriggerClientEvent('esx:showNotification', source, _U('mifepristone'))
	end
end)

ESX.RegisterServerCallback('podol:gethamil', function(source, cb)
	local identifier = GetPlayerIdentifiers(source)[1]

	MySQL.Async.fetchScalar('SELECT pregnant FROM users WHERE identifier = @identifier', {
		['@identifier'] = identifier
	}, function(pregnant)
		if pregnant then
			print(('Pregnant: %s still pregnant and need to go see doctor!'):format(identifier))
		end

		cb(pregnant)
	end)
end)

RegisterServerEvent('podol:sethamil')
AddEventHandler('podol:sethamil', function(pregnant)
	local identifier = GetPlayerIdentifiers(source)[1]

	if type(pregnant) ~= 'boolean' then
		print(('Pregnant: %s attempted to parse something else than a boolean to sethamil!'):format(identifier))
		return
	end

	MySQL.Sync.execute('UPDATE users SET pregnant = @pregnant WHERE identifier = @identifier', {
		['@identifier'] = identifier,
		['@pregnant'] = pregnant
	})
end)

RegisterServerEvent("podol:updatePregnant")
        AddEventHandler("podol:updatePregnant", function()
            local xPlayer = ESX.GetPlayerFromId(source)
            --if xPlayer then
					--local identifier = ESX.GetPlayerFromId(source).identifier
			if xPlayer and xPlayer.identifier then
                    --MySQL.Async.fetchScalar("SELECT `identifier` FROM `last_pregnant` WHERE `identifier`=@identifier", {
                    MySQL.Async.fetchScalar("SELECT `identifier` FROM `users` WHERE `identifier`=@identifier", {
                        --["@identifier"] = identifier,
                        ["@identifier"] = xPlayer.identifier,
                    }, function(identifier)
                        if identifier then
							MySQL.Async.fetchScalar("SELECT `identifier` FROM `last_pregnant` WHERE `identifier`=@identifier", {
								--["@identifier"] = identifier,
								["@identifier"] = xPlayer.identifier,
							}, function(result)
								if result then
									MySQL.Async.execute("UPDATE `last_pregnant` SET `pregnant_time` = @pregnant WHERE `identifier`=@identifier", {
										["@identifier"] = xPlayer.identifier,
										["@pregnant"] = os.time()
									})
								else
									MySQL.Async.execute("INSERT INTO `last_pregnant` (`identifier`, `pregnant_time`) VALUES (@identifier, @pregnant)", {
										["@identifier"] = xPlayer.identifier,
										["@pregnant"] = os.time()
									})
								end
							end)
                            --MySQL.Async.execute("UPDATE `last_pregnant` SET `pregnant_time` = @pregnant WHERE `identifier`=@identifier", {
                                --["@identifier"] = identifier,
                            --    ["@identifier"] = xPlayer.identifier,
                            --    ["@pregnant"] = os.time()
                           -- })
                        --else
							--MySQL.Async.execute("INSERT INTO `last_pregnant` (`identifier`, `pregnant_time`) VALUES (@identifier, --@pregnant)", {
                                --["@identifier"] = identifier,
                             --   ["@identifier"] = xPlayer.identifier,
                            --    ["@pregnant"] = os.time()
                            --})
                        end
                    end)
			--else
                --print(GetPlayerName(xPlayer.source) .. " tried to update their last pregnant, probably using an executor.")
            end
        end)

RegisterServerEvent("podol:removePregnant")
        AddEventHandler("podol:removePregnant", function()
            local xPlayer = ESX.GetPlayerFromId(source)
            --if xPlayer then
					--local identifier = ESX.GetPlayerFromId(source).identifier
			if xPlayer and xPlayer.identifier then
                    MySQL.Async.fetchScalar("SELECT `identifier` FROM `last_pregnant` WHERE `identifier`=@identifier", {
                        --["@identifier"] = identifier
                        ["@identifier"] = xPlayer.identifier
                    }, function(identifier)
                        if identifier then
                            MySQL.Async.execute("DELETE FROM `last_pregnant` WHERE `identifier`=@identifier", {
                                --["@identifier"] = identifier
                                ["@identifier"] = xPlayer.identifier
                            })
                        end
                    end)
			--else
               -- print(GetPlayerName(xPlayer.source) .. " tried to delete their last pregnant, probably using an executor.")
            end
        end)

RegisterServerEvent("podol:checkPregnant")
AddEventHandler("podol:checkPregnant", function()
	local xPlayer = ESX.GetPlayerFromId(source)
	--if xPlayer then
					--local identifier = ESX.GetPlayerFromId(source).identifier
	if xPlayer and xPlayer.identifier then
					MySQL.Async.fetchAll("SELECT * FROM `last_pregnant` WHERE `identifier` = @identifier", {
						--["@identifier"] = identifier
						["@identifier"] = xPlayer.identifier
					}, function(result)
						if result ~= nil then
							local waktu = os.time() - result[1].pregnant_time
							--if os.time() - result[1].pregnant_time >= Config.TimePregnant then
							if waktu >= Config.TimePregnant then
								TriggerClientEvent('podol:updatepregnantStatus', xPlayer.source)
								TriggerClientEvent('esx:showNotification', xPlayer.source, _U('gotohospital'))
							else
								--waktu = os.time() - result[1].pregnant_time
								TriggerClientEvent('esx:showNotification', xPlayer.source, _U('notready') .. math.floor((Config.TimePregnant - waktu)) .. ' seconds')
							end
						else
							TriggerClientEvent('esx:showNotification', xPlayer.source, _U('notpregnant'))
						end
					end)
				end
end)