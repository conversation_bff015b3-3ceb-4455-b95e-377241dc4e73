Shared = {
    Locale = 'en',

    Notify = {
        duration = 5000, -- miliseconds
        position = 'top-right' -- 'top' or 'top-right' or 'top-left' or 'bottom' or 'bottom-right' or 'bottom-left' or 'center-right' or 'center-left'
    },
    
    Logs = {
        enabled = true,
        webhook = 'https://discordapp.com/api/webhooks/1382057168655880322/tri3nDG0sv2OKG3ZME5HgHAV7CsVwP-v4Dcv31fajtEgVGxUqux5QCQDdTMv3V45avQd',
        servername = 'Zona Segura',
        color = 6579450,
        icon_url = 'https://dunb17ur4ymx4.cloudfront.net/webstore/logos/248d77847b320034db12949cf577772b654daadd.png'
    },

    Commands = {
        safezonecreator = {
            name = 'safezonecreator',       -- command for accessing creator menu: create safe zone, delete safe zone, safe zones list
            restricted = 'group.admin',     -- group that will have acces to command 
        }
    },

    Keys = { -- https://docs.fivem.net/docs/game-references/controls/
        finish = 191,
        cancel = 194,
        finishPlacingPoints = 47,
    },

    TextUI_Position = 'top-center', -- 'right-center' or 'left-center' or 'top-center'
    
    TextUI = {
        text = 'Has Entrado A Zona Segura',
        icon = 'shield-alt',
        style = {
            borderRadius = 5,
            backgroundColor = '#6464fa',
            color = 'white'
        }
    },

    CreatorOptions = { -- enable or disable options in safe zone admin creator menu - *THIS ONLY DECIDES WHETHER THESE FEATURES WILL BE ENABLED WHEN CREATING THE SAFE ZONE, IF YES THE ADMIN WILL STILL HAVE TO CHECK/UNCHECK THE OPTIONS WHEN CREATING THE SAFE ZONE IF HE WANT TO TURN THEM ON/OFF FOR THE SAFE ZONE*
        maxVehSpeed = true,
        textUI = true,
        driveBy = true,
        weapons = true,
        players_invisible = true,
        vehicles_invisible = true,
        debugMode = true
    },

    MaxSpeedMultiplier = 3.6, -- 3.6 for kmh, 2.236936 for mph
    MaxPoints = 30, -- the maximum amount of points that can be made
    ClosestRadius = 10, -- radius in which the script will search for the nearest safe zone that needs to be deleted when the "closest" option is selected
}

-- Framework
ESX = exports["es_extended"]:getSharedObject()