Config = {}

Config.Stores = {
    {
        storeLocation = {-157.7605, -297.4547, 39.7333},
        npcSpawn = {-167.7280, -301.2187, 39.7333, 294.8534},
        startRobbery = {-154.1218, -295.8957, 39.7333},
        theftPoints = {
            {-156.3416, -296.3548, 39.7333},
            {-159.9006, -295.0817, 39.7333},
            {-165.6610, -311.6837, 39.7333},
            {-158.7546, -308.9388, 39.7333},
            {-159.4050, -300.1932, 39.7333},
        },
        cashRegisters = {
            {-165.1456, -303.4498, 39.7333}
        }
    },
    {
        storeLocation = {122.5545, -221.8009, 54.5447},
        npcSpawn = {117.6352, -234.0868, 54.5578, 340.1921},
        startRobbery = {116.4735, -227.9309, 54.5578},
        theftPoints = {
            {121.7897, -226.6385, 54.5578},
            {120.7040, -221.7944, 54.5578},
            {120.3410, -215.9095, 54.5578},
            {122.8663, -208.9711, 54.5578},
            {131.0764, -214.7417, 54.5578},

        },
        cashRegisters = {
            {126.9299, -224.4677, 54.5578}
        }
    },
    {
        storeLocation = {-1189.4266, -769.3534, 17.3247},
        npcSpawn = {-1180.6365, -763.8762, 17.3265, 127.5266},
        startRobbery = {-1183.0751, -769.5364, 17.3320},
        theftPoints = {
            {-1190.7648, -771.4176, 17.3262},
            {-1197.7899, -769.0941, 17.3148},
            {-1197.4836, -780.9374, 17.3326},
            {-1191.9650, -776.8334, 17.3327},
        },
        cashRegisters = {
            {-1193.7950, -766.9475, 17.3164}
        }
    },
    {
        storeLocation = {76.2383, -1394.1940, 29.3762},
        npcSpawn = {71.0857, -1389.6648, 29.3762, 274.0051},
        startRobbery = {74.0834, -1387.6262, 29.3761},
        theftPoints = {
            {80.9929, -1393.7012, 29.3762},
            {81.6333, -1400.3726, 29.3817},
            {75.3073, -1400.2684, 29.3762},
            {77.9786, -1396.2292, 29.4006},
        },
        cashRegisters = {
            {73.8850, -1391.9706, 29.3762}
        }
    },
    -- Add more stores as needed
}


-- ### IMPORTANT, ENSURE TO ADD THESE ITEMS TO YOUR INVENTORY ITEM LIST ### ---
Config.RewardItems = {
    {item = 'sneakers', count = 1},
    {item = 'boots', count = 1},
    {item = 'sandals', count = 1},
    {item = 'tshirt', count = 1},
    {item = 'jacket', count = 1},
    {item = 'jeans', count = 1},
    {item = 'shorts', count = 1},
    {item = 'hat', count = 1},
    {item = 'sunglasses', count = 1},
}

Config.RegisterMoneyAmount = 500 -- This will be the money the player gets if they kill the npc then loot the register

-- Define robbery cooldown in seconds
Config.theftCooldown = 600000 -- 10 minutes (defined in milliseconds) eg, 300000 would be 5 minutes.

-- Define random event chances (out of 100)
Config.NPCEventChance = 100 -- 20% chance of NPC arrival


Config.NotificationSystem = 'esx' -- | esx | qbcore | mythic | codewave | custom


Config.DispatchSystem = 'custom' -- aty | quasar | custom 

