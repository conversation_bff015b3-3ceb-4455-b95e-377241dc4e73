Config = {}

--Relationship Setup
AddRelationshipGroup("SUB_CREW") --creates a new relationship group for submarine crew
SetRelationshipBetweenGroups(5, "SUB_CREW", "PLAYER") --sets submarine crew to hate player peds
SetRelationshipBetweenGroups(0, "SUB_CREW", "SUB_CREW") --sets submarine crew to be companions with each other

Config.Peds = {
	---
  { model="s_f_y_stripper_02", 
  x=123.65, y=-1294.95, z=29.27, a=22.18,
  anim="mini@strip_club@private_dance@idle", 
  animName="priv_dance_idle",
  stoic=true,
  voice=female,
  soldier=false,
  lightarms=false,
  mediumarms=false,
  heavyarms=false,
  relationship=nil,
	god=false,
	task=nil,
  extrahealth=false},
  ---
  { model="s_f_y_stripper_02", 
  x=111.99, y=-1286.03, z=28.46, a=308.8, 
  anim="mini@strip_club@lap_dance@ld_girl_a_song_a_p1", 
  animName="ld_girl_a_song_a_p1_f",
  stoic=true,
  voice=female,
  soldier=false,
  lightarms=false,
  mediumarms=false,
  heavyarms=false,
  relationship=nil,
	god=false,
	task=nil,
  extrahealth=false},
  --- 
  { model="csb_stripper_01", 
  x=108.72, y=-1289.33, z=28.86, a=295.53, 
  anim="mini@strip_club@private_dance@part2", 
  animName="priv_dance_p2",
  stoic=true,
  voice=female,
  soldier=false,
  lightarms=false,
  mediumarms=false,
  heavyarms=false,
  relationship=nil,
	god=false,
	task=nil,
  extrahealth=false},
    ---
  { model="csb_bogdan",
  x=509.66, y=4827.44, z=-62.59, a=260.44,
  anim=nil,
  animName=nil,
  stoic=false,
  voice=male,
  soldier=true,
  lightarms=true,
  mediumarms=false,
  heavyarms=false,
  relationship="SUB_CREW",
  god=false,
  task=nil,
  extrahealth=false},
  ---
  { model="csb_bogdan",
  x=510.41, y=4827.04, z=-66.19, a=273.42,
  anim=nil,
  animName=nil,
  stoic=false,
  voice=male,
  soldier=true,
  lightarms=true,
  mediumarms=false,
  heavyarms=false,
  relationship="SUB_CREW",
  god=false,
  task=nil,
  extrahealth=false},
  --- 
  { model="csb_bogdan",
  x=513.23, y=4841.18, z=-66.19, a=264.93,
  anim=nil,
  animName=nil,
  stoic=false,
  voice=male,
  soldier=true,
  lightarms=true,
  mediumarms=false,
  heavyarms=false,
  relationship="SUB_CREW",
  god=false,
  task=nil,
  extrahealth=false},
    ---
  { model="csb_bogdan",
  x=514.23, y=4900.19, z=-65.77, a=174.41,
  anim=nil,
  animName=nil,
  stoic=false,
  voice=male,
  soldier=true,
  lightarms=true,
  mediumarms=false,
  heavyarms=false,
  relationship="SUB_CREW",
  god=false,
  task=nil,
  extrahealth=false},
  ---
  { model="csb_bogdan",
  x=513.68, y=4885.4, z=-66.19, a=346.11,
  anim=nil,
  animName=nil,
  stoic=false,
  voice=male,
  soldier=true,
  lightarms=false,
  mediumarms=true,
  heavyarms=false,
  relationship="SUB_CREW",
  god=false,
  task=nil,
  extrahealth=false},
  --- 
  { model="csb_bogdan",
  x=515.62, y=4875.45, z=-62.59, a=353.16,
  anim=nil,
  animName=nil,
  stoic=false,
  voice=male,
  soldier=true,
  lightarms=false,
  mediumarms=true,
  heavyarms=false,
  relationship="SUB_CREW",
  god=false,
  task=nil,
  extrahealth=false},
    ---
  { model="csb_bogdan",
  x=513.03, y=4850.09, z=-62.59, a=180.31,
  anim=nil,
  animName=nil,
  stoic=false,
  voice=male,
  soldier=true,
  lightarms=false,
  mediumarms=true,
  heavyarms=false,
  relationship="SUB_CREW",
  god=false,
  task=nil,
  extrahealth=false},
  ---
  { model="mp_m_bogdangoon",
  x=514.1, y=4829.48, z=-68.94, a=130.54,
  anim=nil,
  animName=nil,
  stoic=false,
  voice=male,
  soldier=true,
  lightarms=false,
  mediumarms=false,
  heavyarms=true,
  relationship="SUB_CREW",
  god=false,
  task=nil,
  extrahealth=true},
  --- 
  { model="mp_m_bogdangoon",
  x=515.48, y=4874.71, z=-68.99, a=82.17,
  anim=nil,
  animName=nil,
  stoic=false,
  voice=male,
  soldier=true,
  lightarms=false,
  mediumarms=false,
  heavyarms=true,
  relationship="SUB_CREW",
  god=false,
  task=nil,
  extrahealth=true},
    ---
  { model="mp_m_bogdangoon",
  x=516.95, y=4823.75, z=-66.19, a=3.42,
  anim=nil,
  animName=nil,
  stoic=false,
  voice=male,
  soldier=true,
  lightarms=false,
  mediumarms=false,
  heavyarms=true,
  relationship="SUB_CREW",
  god=false,
  task=nil,
  extrahealth=true},
    ---
}
