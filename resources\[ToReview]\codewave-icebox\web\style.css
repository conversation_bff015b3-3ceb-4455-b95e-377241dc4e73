@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&family=Open+Sans:wght@400;700&display=swap');

body {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(to bottom, #0a0a33, #1a1a5e, #2a2a88); /* Deep blue to royal blue background */
    font-family: 'Roboto', sans-serif;
    color: #f5f5f5; /* Light grey text */
}

#menu-container, #crafting-container {
    display: flex;
    flex-direction: column;
    width: 95%;
    max-width: 1200px;
    background: #ffffff; /* Diamond white */
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
}

#menu-header, #crafting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #1a1a5e; /* Royal blue */
    color: #f5f5f5; /* Light grey text */
    border-bottom: 3px solid #cccccc; /* Silver border */
    box-shadow: inset 0 -2px 5px rgba(0, 0, 0, 0.3);
}

#menu-header .header-logo, #crafting-header .header-logo, #info-header .header-logo {
    height: 210px;
    margin: 0 auto;
}

#menu-header .header-icon, #crafting-header .header-icon {
    margin: 0 10px;
}

.jewel-logo {
    height: 260px;
    width: auto;
    animation: sparkle 2s infinite;
}

@keyframes sparkle {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

#close-button, #close-crafting-button {
    background: none;
    border: none;
    color: #f5f5f5; /* Light grey text */
    font-size: 28px;
    cursor: pointer;
}

#menu, #crafting-menu {
    display: flex;
    flex-wrap: wrap;
    padding: 25px;
    gap: 20px;
    overflow-y: auto;
    max-height: 600px;
    justify-content: center;
}

#menu::-webkit-scrollbar, #crafting-menu::-webkit-scrollbar {
    width: 12px;
}

#menu::-webkit-scrollbar-track, #crafting-menu::-webkit-scrollbar-track {
    background: #1a1a5e; /* Royal blue */
    border-radius: 10px;
}

#menu::-webkit-scrollbar-thumb, #crafting-menu::-webkit-scrollbar-thumb {
    background-color: #cccccc; /* Silver */
    border-radius: 10px;
    border: 3px solid #ffffff; /* Diamond white */
}

#menu::-webkit-scrollbar-thumb:hover, #crafting-menu::-webkit-scrollbar-thumb:hover {
    background-color: #b3b3b3; /* Darker silver */
}

.menu-item, .crafting-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 220px;
    padding: 20px;
    background: #f1f1f1; /* Light grey */
    color: #0a0a33; /* Deep blue text */
    border-radius: 10px;
    cursor: pointer;
    transition: background 0.3s, transform 0.3s, box-shadow 0.3s;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
}

.menu-item img, .crafting-item img {
    height: 200px;
    width: 200px;
    margin-bottom: 15px;
    border-radius: 50%;
    border: 2px solid #cccccc; /* Silver border */
    padding: 5px;
    background: #ffffff; /* Diamond white background */
}

.menu-item span, .crafting-item span {
    font-size: 18px;
    text-align: center;
    color: #333333; /* Dark grey text */
}

.menu-item .item-price {
    font-size: 18px;
    margin: 8px 0;
    color: #1a1a5e; /* Royal blue */
}

.menu-item input, .crafting-item input {
    width: 70px;
    margin-top: 12px;
    border: 2px solid #cccccc; /* Silver */
    border-radius: 8px;
    padding: 8px;
    text-align: center;
    font-size: 16px;
    outline: none;
    transition: border 0.3s;
    background: #ffffff; /* Diamond white background */
    color: #0a0a33; /* Deep blue text */
}

.menu-item input:focus, .crafting-item input:focus {
    border-color: #1a1a5e; /* Royal blue */
}

.menu-item button, .crafting-item button {
    margin-top: 12px;
    padding: 12px;
    border: none;
    border-radius: 8px;
    background: #1a1a5e; /* Royal blue */
    color: #ffffff; /* Diamond white text */
    cursor: pointer;
    font-size: 18px;
    transition: background 0.3s;
}

.menu-item button:hover, .crafting-item button:hover {
    background: #0a0a33; /* Darker royal blue */
}

.menu-item:hover, .crafting-item:hover {
    background: #cccccc; /* Silver */
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

#notification-container {
    position: fixed;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
    z-index: 1000;
}

.notification {
    padding: 18px 35px;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.7);
    font-size: 20px;
    animation: fadeInOut 4s forwards;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #f5f5f5; /* Light grey text */
}

.notification.success {
    background: #28a745; /* Emerald */
}

.notification.error {
    background: #e74c3c; /* Ruby red */
}

@keyframes fadeInOut {
    0% { opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { opacity: 0; }
}

.spinner {
    width: 60px;
    height: 60px;
    border: 6px solid rgba(245, 245, 245, 0.3); /* Light grey */
    border-radius: 50%;
    border-top-color: #cccccc; /* Silver */
    animation: spin 1s ease-in-out infinite;
    margin: 25px auto;
    position: relative;
}

.spinner-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    color: #cccccc; /* Silver */
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

#info-container {
    display: flex;
    flex-direction: column;
    width: 95%;
    max-width: 1200px;
    background: #ffffff; /* Diamond white */
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

#info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #1a1a5e; /* Royal blue */
    color: #f5f5f5; /* Light grey text */
    border-bottom: 3px solid #cccccc; /* Silver border */
    box-shadow: inset 0 -2px 5px rgba(0, 0, 0, 0.3);
}

#info-header .header-logo {
    height: 80px;
    margin: 0 auto;
}

#close-info-button {
    background: none;
    border: none;
    color: #f5f5f5; /* Light grey text */
    font-size: 28px;
    cursor: pointer;
}

#info-content {
    padding: 25px;
    color: #0a0a33; /* Deep blue text */
}

.themed-button {
    margin: 20px auto;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #1a1a5e, #0a0a33); /* Royal blue gradient */
    color: #ffffff; /* Diamond white text */
    cursor: pointer;
    font-size: 18px;
    transition: background 0.3s, transform 0.3s;
    display: block;
    text-align: center;
}

.themed-button:hover {
    background: linear-gradient(135deg, #0a0a33, #000033); /* Darker royal blue gradient */
    transform: scale(1.05);
}

.required-items {
    font-family: 'Open Sans', sans-serif;
    background: #1a1a5e; /* Royal blue */
    padding: 10px;
    border-radius: 8px;
    margin-top: 10px;
    width: 90%;
    text-align: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    text-transform: uppercase;
}

.required-items div {
    margin: 5px 0;
    color: #f5f5f5; /* Light grey text */
    font-weight: 700;
    font-size: 16px;
}

.menu-item, .crafting-item {
    position: relative;
    background: #ffffff; /* Diamond white */
    color: #0a0a33; /* Deep blue text */
    border: 1px solid #1a1a5e; /* Royal blue border */
}

.menu-item:hover, .crafting-item:hover {
    background: #f1f1f1; /* Slightly darker grey for hover effect */
}

#crafting-description {
    margin-top: 20px;
    padding: 15px;
    background: #ffffff; /* Diamond white */
    color: #0a0a33; /* Deep blue text */
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    text-align: center;
}

#crafting-description h2 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

#crafting-description p {
    font-size: 16px;
    color: #333333; /* Dark grey text */
}

