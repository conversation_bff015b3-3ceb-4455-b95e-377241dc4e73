function sendNotify(title, description, type)
    lib.notify({
        title = title,
        description = description,
        duration = Shared.Notify.duration,
        type = type,
        position = Shared.Notify.position
    })
end

RegisterNetEvent('safezonecreator:notify', sendNotify)

-- Custom Exports/Triggers/Code...
onEnterFunctions = function(safezone) -- once the player enters the safezone
    -- your code
end

onExitFunctions = function(safezone) -- once the player leaves the safezone
    -- your code
end

insideFunctions = function(safezone) -- while the player is inside the safe zone 
    -- your code
end

--[[
    "safezone" values:
    - id
    - label
    - data:
        - points (table):
            - x, y, z
        - maxSpeed
        - vInvisible
        - textUI
        - debug
        - weapons
        - pInvisible
        - driveBy

    For example:
    If you want to get safezone's label, you must do "safezone.label" etc..
]]