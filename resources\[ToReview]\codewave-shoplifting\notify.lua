

--- If you do not know how to add a notification then go ahead and open a ticket up discord.gg/codewave

function Notify(message, type)
    if Config.NotificationSystem == 'esx' then
        ESX.ShowNotification(message)
    elseif Config.NotificationSystem == 'qbcore' then
        QBCore.Functions.Notify(message, type)
    elseif Config.NotificationSystem == 'mythic' then
        exports['mythic_notify']:DoHudText(type, message)
    elseif Config.NotificationSystem == 'codewave' then
        exports['Codewave-Notify']:Alert("INFO", message, 5000, 'warning')
    elseif Config.NotificationSystem == 'custom' then
        -- ensure to add "message" to your custom notify.
    else
        -- Default fallback notification
        print('Notification: ' .. message)
    end
end



--- If you do not know how to add a custom dispatch then go ahead and open a ticket up discord.gg/codewave
function DispatchPolice(storeLocation)
    if Config.DispatchSystem == 'aty' then
        exports['aty_dispatch']:SendDispatch('Shoplifters', 'Robbery in progress', 40, {'police', 'sheriff'})
    elseif Config.DispatchSystem == 'quasar' then
        TriggerServerEvent('qs-dispatch:server:CreateDispatchCall', {
            job = { 'police', 'sheriff', 'traffic', 'patrol' },
            callLocation = storeLocation,
            callCode = { code = '10-32', snippet = 'Shoplifters!' },
            message = "People are stealing from luxury clothing stores",
            flashes = false, -- you can set to true if you need call flashing sirens...
            blip = {
                sprite = 488, --blip sprite
                scale = 1.5, -- blip scale
                colour = 1, -- blio colour
                flashes = true, -- blip flashes
                text = 'Shoplifters', -- blip text
                time = (20 * 1000), --blip fadeout time (1 * 60000) = 1 minute
            },  
        })
    elseif Config.DispatchSystem == 'custom' then
        -- Implement custom dispatch logic here
    else
        -- Default fallback dispatch
        print('Dispatch to police at location: ', storeLocation)
    end
end