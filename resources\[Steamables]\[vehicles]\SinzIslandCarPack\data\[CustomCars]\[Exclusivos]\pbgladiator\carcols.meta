<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVarGlobal>
<Kits>
 <Item>
<kitName>6453433_jeepgladiator_modkit</kitName>
<id value="6453433" />	 	
<kitType>MKT_SPECIAL</kitType>
<visibleMods>


 <!-- 
=================================
             B O O S T
=================================
-->
        
        <Item>
          <modelName>pbgladiator_boost1</modelName>
          
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
          </Item>
          
          <Item>
          <modelName>pbgladiator_boost2</modelName>
          
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
          </Item>

          <Item>
          <modelName>pbgladiator_boost3</modelName>
          
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
          </Item> 













		<Item>
<modelName>jeepg_chassi_1</modelName>
<modShopLabel>45373475</modShopLabel>
<linkedModels />
 <turnOffBones>
    <Item>misc_t</Item>
    <Item>misc_g</Item>
    <Item>misc_h</Item>
     <Item>misc_l</Item>
  </turnOffBones>
<type>VMT_CHASSIS</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="0" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>
		<Item>
<modelName>jeepg_chassi_2</modelName>
<modShopLabel>jeepg_4573457437chassi_2</modShopLabel>
<linkedModels />
 <turnOffBones>
    <Item>misc_t</Item>
    <Item>misc_g</Item>
    <Item>misc_h</Item>
     <Item>misc_l</Item>
  </turnOffBones>
<type>VMT_CHASSIS</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="0" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>
		<!-- <Item>
<modelName>jeepg_chassi_3</modelName>
<modShopLabel>jeepg_chassi_3</modShopLabel>
<linkedModels />
<turnOffBones>
</turnOffBones>
<type>VMT_CHASSIS</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="0" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item> -->
		<Item>
<modelName>jeepg_chassi_4</modelName>
<modShopLabel>475347435</modShopLabel>
<linkedModels />
  <turnOffBones>
    <Item>misc_t</Item>
    <Item>misc_g</Item>
    <Item>misc_h</Item>
    <Item>misc_l</Item>
  </turnOffBones>
<type>VMT_CHASSIS</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="0" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>
		<Item>
	 <modelName>jeepg_roof_1</modelName>
	 
<linkedModels />
<turnOffBones>
</turnOffBones>
<type>VMT_ROOF</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="20" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>
		<Item>
	 <modelName>jeepg_roof_2</modelName>
	 
<linkedModels />
<turnOffBones>
</turnOffBones>
<type>VMT_ROOF</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="20" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>
		<Item>
	 <modelName>jeepg_roof_3</modelName>
	 
<linkedModels />
<turnOffBones>
</turnOffBones>
<type>VMT_ROOF</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="20" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>

<!-- CUSTOM LIVERY -->

		<Item>
	 <modelName>pbgladiator_livery_1</modelName>
	
<linkedModels />
<turnOffBones>
</turnOffBones>
<type>VMT_LIVERY_MOD</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="0" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>

		<Item>
	 <modelName>pbgladiator_livery_2</modelName>
	 
<linkedModels />
<turnOffBones>
</turnOffBones>
<type>VMT_LIVERY_MOD</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="0" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>

		<Item>
	 <modelName>pbgladiator_livery_3</modelName>
	 
<linkedModels />
<turnOffBones>
</turnOffBones>
<type>VMT_LIVERY_MOD</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="0" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>

		<Item>
	 <modelName>pbgladiator_livery_4</modelName>
	 
<linkedModels />
<turnOffBones>
</turnOffBones>
<type>VMT_LIVERY_MOD</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="0" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>

		<Item>
	 <modelName>pbgladiator_livery_5</modelName>
	 
<linkedModels />
<turnOffBones>
</turnOffBones>
<type>VMT_LIVERY_MOD</type>
<bone>chassis</bone>
<collisionBone>chassis</collisionBone>
<cameraPos>VMCP_DEFAULT</cameraPos>
<audioApply value="1.000000" />
<weight value="0" />
<turnOffExtra value="false" />
<disableBonnetCamera value="false" />
<allowBonnetSlide value="true" />
</Item>




<!-- GRILLL (misc_a)-->
	 <Item>
      <modelName>pbjgladiator_grill1</modelName>
     
      <linkedModels/>
        <turnOffBones>
          <Item>misc_a</Item>
        </turnOffBones>
      <type>VMT_GRILL</type>
      <bone>chassis</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

	 <Item>
      <modelName>pbjgladiator_grill3</modelName>
     
      <linkedModels/>
        <turnOffBones>
          <Item>misc_a</Item>
        </turnOffBones>
      <type>VMT_GRILL</type>
      <bone>chassis</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

	 <Item>
      <modelName>pbjgladiator_grill4</modelName>
      
      <linkedModels/>
        <turnOffBones>
          <Item>misc_a</Item>
        </turnOffBones>
      <type>VMT_GRILL</type>
      <bone>chassis</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbjgladiator_grill5</modelName>
      
      <linkedModels/>
        <turnOffBones>
          <Item>misc_a</Item>
        </turnOffBones>
      <type>VMT_GRILL</type>
      <bone>chassis</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName></modelName>
      
      <linkedModels/>
        <turnOffBones>
          <Item>misc_a</Item>
        </turnOffBones>
      <type>VMT_GRILL</type>
      <bone>chassis</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>















 <!-- <Item>
      <modelName></modelName>
      
      <linkedModels/>
        <turnOffBones>
          <Item>misc_b</Item>
        </turnOffBones>
      <type>VMT_SKIRT</type>
      <bone>chassis</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item> -->







<!-- BONNET -->

 <Item>
      <modelName>pbgladiator_bonnet1</modelName>
      
      <linkedModels/>
        <turnOffBones>
          <Item>bonnet</Item>
        </turnOffBones>
      <type>VMT_BONNET</type>
      <bone>bonnet</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <!-- <Item>
      <modelName>pbgladiator_bonnet2</modelName>
      <modShopLabel>RIATA_SKIRT1</modShopLabel>
      <linkedModels/>
        <turnOffBones>
          <Item>bonnet</Item>
        </turnOffBones>
      <type>VMT_BONNET</type>
      <bone>bonnet</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item> -->



<!-- ENGINE (misc_d) -->
 <Item>
      <modelName>pbgladiator_engine1</modelName>
     
      <linkedModels/>
        <turnOffBones>
          <Item>misc_d</Item>
        </turnOffBones>
      <type>VMT_ENGINEBAY1</type>
      <bone>chassis</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator_engine2</modelName>
     
      <linkedModels/>
        <turnOffBones>
          <Item>misc_d</Item>
        </turnOffBones>
      <type>VMT_ENGINEBAY1</type>
      <bone>misc_d</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>





<!-- BELAKANG Biasa (misc_t) -->
 
 <Item>
      <modelName>pbgladiator_wing1</modelName>
      
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          <Item>misc_l</Item>
          <Item>misc_h</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator_wing2</modelName>
    
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator_wing3</modelName>
    
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator_wing4</modelName>
     
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>null</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator_wing5</modelName>
      
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator_wing6</modelName>
     
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>misc_l</Item>
          <Item>null</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator_wing7</modelName>
   
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator_wing8</modelName>
   
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>




<!-- Engine -->


 <Item>
      <modelName>pbgladiator_engine1</modelName>
      
      <linkedModels/>
        <turnOffBones>
          <Item>misc_d</Item>
        </turnOffBones>
      <type>VMT_ENGINEBAY1</type>
      <bone>chassis</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>







<!-- BELAKANG Biasa (misc_t) -->
 
 <Item>
      <modelName>pbgladiator__glow_wing1</modelName>
     
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator__glow_wing2</modelName>
    
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator__glow_wing3</modelName>
    
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator__glow_wing4</modelName>
    
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator__glow_wing5</modelName>
 
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <!-- <Item>
      <modelName>pbgladiator__glow_wing6</modelName>
      <modShopLabel>RIATA_SKIRT1</modShopLabel>
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>chassis</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item> -->

 <Item>
      <modelName>pbgladiator__glow_wing7</modelName>
    
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>misc_l</Item>
          <Item>null</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator__glow_wing8</modelName>
    
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator__glow_wing9</modelName>
     
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_f</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_t</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>


<!-- Roof (misc_f) -->

 <Item>
      <modelName>pbgladiator__misc_f_1</modelName>
     
      <linkedModels/>
        <turnOffBones>
          <Item>misc_f</Item>
        </turnOffBones>
      <type>VMT_CHASSIS2</type>
      <bone>chassis</bone>
      <collisionBone>bonnet</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>



<!-- Tayar (misc_i) -->

 <Item>
      <modelName>pbgladiator_tayar_1</modelName>
    
      <linkedModels/>
        <turnOffBones>
          <Item>null</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
        </turnOffBones>
      <type>VMT_CHASSIS3</type>
      <bone>misc_i</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbgladiator_tayar_2</modelName>
    
      <linkedModels/>
        <turnOffBones>
          <Item>null</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
        </turnOffBones>
      <type>VMT_CHASSIS3</type>
      <bone>misc_i</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>






<!-- Bumper_r (misc_k) -->

 <Item>
      <modelName>pbjgladiator_bumper_r_1</modelName>
  
      <linkedModels/>
        <turnOffBones>
          <Item>misc_o</Item>
        </turnOffBones>
      <type>VMT_BUMPER_R</type>
      <bone>misc_l</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbjgladiator_bumper_r_2</modelName>
   
      <linkedModels/>
        <turnOffBones>
          <Item>misc_o</Item>
        </turnOffBones>
      <type>VMT_BUMPER_R</type>
      <bone>misc_l</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>







<!-- Bumbung (misc_e) -->

 <Item>
      <modelName>pbjgladiator_bumbung_1</modelName>
     
      <linkedModels/>
        <turnOffBones>
          <Item>misc_e</Item>
        </turnOffBones>
      <type>VMT_SPOILER</type>
      <bone>misc_e</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

 <Item>
      <modelName>pbjgladiator_bumbung_2</modelName>
    
      <linkedModels/>
        <turnOffBones>
          <Item>misc_e</Item>
        </turnOffBones>
      <type>VMT_SPOILER</type>
      <bone>misc_e</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>



<!-- Belakang (Aniamted)-->

<!-- <Item>
      <modelName>pbgladiator_anim_wing88</modelName>
      <modShopLabel>sfsfe</modShopLabel>
      <linkedModels/>
        <turnOffBones>
          <Item>misc_t</Item>
          <Item>misc_g</Item>
          <Item>misc_h</Item>
          <Item>null</Item>
          <Item>misc_l</Item>
        </turnOffBones>
      <type>VMT_CHASSIS</type>
      <bone>misc_l</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item> -->



















































<!-- SKIRT (misc_b) -->

  <Item>
      <modelName>pbjgladiator_skirt1_a1</modelName>
      <linkedModels>
        <Item>pbjgladiator_skirt1_a2</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item> <!-- misc_p -->
          <Item>misc_q</Item> <!-- misc_q -->
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_s</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

  <Item>
      <modelName>pbjgladiator_skirt1_b1</modelName>
      <linkedModels>
        <Item>pbjgladiator_skirt1_b2</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item>
          <Item>misc_q</Item>
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_p</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>
  
  <Item>
      <modelName>pbjgladiator_skirt1_c1</modelName>
      <linkedModels>
        <Item>pbjgladiator_skirt1_c2</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item>
          <Item>misc_q</Item>
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_s</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>


  <Item>
      <modelName>pbjgladiator_customtext2</modelName>
      <linkedModels>
        <Item>pbjgladiator_customtext1</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item>
          <Item>misc_q</Item>
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_s</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

  <Item>
      <modelName>pbgladiator_brian1</modelName>
      <linkedModels>
        <Item>pbgladiator_brian2</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item>
          <Item>misc_q</Item>
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_s</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

  <Item>
      <modelName>pbgladiator_Dominic1</modelName>
      <linkedModels>
        <Item>pbgladiator_Dominic2</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item>
          <Item>misc_q</Item>
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_s</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

  <Item>
      <modelName>pbgladiator_han1</modelName>
      <linkedModels>
        <Item>pbgladiator_han2</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item>
          <Item>misc_q</Item>
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_s</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>


  <Item>
      <modelName>pbgladiator_letty1</modelName>
      <linkedModels>
        <Item>pbgladiator_letty2</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item>
          <Item>misc_q</Item>
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_s</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>


  <Item>
      <modelName>pbgladiator_osman1</modelName>
      <linkedModels>
        <Item>pbgladiator_osman2</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item>
          <Item>misc_q</Item>
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_s</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

  <Item>
      <modelName>pbgladiator_bollgaming2</modelName>
      <linkedModels>
        <Item>pbgladiator_bollgaming1</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item>
          <Item>misc_q</Item>
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_s</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

  <Item>
      <modelName>pbgladiator_zack1</modelName>
      <linkedModels>
        <Item>pbgladiator_zack2</Item>
		  </linkedModels>
        <turnOffBones>
          <Item>misc_p</Item>
          <Item>misc_q</Item>
        </turnOffBones>
      <type>VMT_WING_L</type>
      <bone>misc_s</bone>
      <collisionBone>chassis</collisionBone>
      <cameraPos>VMCP_DEFAULT</cameraPos>
      <audioApply value="1.000000" />
      <weight value="10" />
      <turnOffExtra value="false" />
      <disableBonnetCamera value="false" />
      <allowBonnetSlide value="true" />
  </Item>

  
  <Item>
    <modelName>pbgladiator_paktammacarov</modelName>
    <linkedModels>
      <Item>pbgladiator_tengokapadek</Item>
    </linkedModels>
      <turnOffBones>
        <Item>misc_p</Item>
        <Item>misc_q</Item>
      </turnOffBones>
    <type>VMT_WING_L</type>
    <bone>misc_s</bone>
    <collisionBone>chassis</collisionBone>
    <cameraPos>VMCP_DEFAULT</cameraPos>
    <audioApply value="1.000000" />
    <weight value="10" />
    <turnOffExtra value="false" />
    <disableBonnetCamera value="false" />
    <allowBonnetSlide value="true" />
</Item>


</visibleMods>
  <linkMods>




    <Item>
      <modelName>pbgladiator_tengokapadek</modelName>
      <bone>misc_r</bone>
      <turnOffExtra value="false" />
    </Item>	




		<Item>
          <modelName>pbjgladiator_skirt1_a2</modelName>
          <bone>misc_r</bone>
          <turnOffExtra value="false" />
        </Item>	
	 

		<Item>
          <modelName>pbjgladiator_skirt1_b2</modelName>
          <bone>misc_q</bone>
          <turnOffExtra value="false" />
        </Item>	
        
	<Item>
          <modelName>pbjgladiator_skirt1_c2</modelName>
          <bone>misc_r</bone>
          <turnOffExtra value="false" />
        </Item>	

	<Item>
          <modelName>pbjgladiator_customtext1</modelName>
          <bone>misc_r</bone>
          <turnOffExtra value="false" />
        </Item>	



<Item>
          <modelName>pbgladiator_brian2</modelName>
          <bone>misc_r</bone>
          <turnOffExtra value="false" />
        </Item>	

<Item>
          <modelName>pbgladiator_Dominic2</modelName>
          <bone>misc_r</bone>
          <turnOffExtra value="false" />
        </Item>	

<Item>
          <modelName>pbgladiator_han2</modelName>
          <bone>misc_r</bone>
          <turnOffExtra value="false" />
        </Item>	

<Item>
          <modelName>pbgladiator_letty2</modelName>
          <bone>misc_r</bone>
          <turnOffExtra value="false" />
        </Item>	

<Item>
          <modelName>pbgladiator_osman2</modelName>
          <bone>misc_r</bone>
          <turnOffExtra value="false" />
        </Item>	

<Item>
          <modelName>pbgladiator_bollgaming1</modelName>
          <bone>misc_r</bone>
          <turnOffExtra value="false" />
        </Item>	

<Item>
          <modelName>pbgladiator_zack2</modelName>
          <bone>misc_r</bone>
          <turnOffExtra value="false" />
        </Item>	










	</linkMods>




<statMods>
<Item>
<identifier />
<modifier value="20" />
<audioApply value="1.000000" />
<weight value="20" />
<type>VMT_ENGINE</type>
</Item>
<Item>
<identifier />
<modifier value="40" />
<audioApply value="1.000000" />
<weight value="20" />
<type>VMT_ENGINE</type>
</Item>
<Item>
<identifier />
<modifier value="75" />
<audioApply value="1.000000" />
<weight value="20" />
<type>VMT_ENGINE</type>
</Item>
<Item>
<identifier />
<modifier value="100" />
<audioApply value="1.000000" />
<weight value="20" />
<type>VMT_ENGINE</type>
</Item>
<Item>
<identifier />
<modifier value="25" />
<audioApply value="1.000000" />
<weight value="5" />
<type>VMT_BRAKES</type>
</Item>
<Item>
<identifier />
<modifier value="50" />
<audioApply value="1.000000" />
<weight value="5" />
<type>VMT_BRAKES</type>
</Item>
<Item>
<identifier />
<modifier value="100" />
<audioApply value="1.000000" />
<weight value="5" />
<type>VMT_BRAKES</type>
</Item>
<Item>
<identifier />
<modifier value="25" />
<audioApply value="1.000000" />
<weight value="5" />
<type>VMT_GEARBOX</type>
</Item>
<Item>
<identifier />
<modifier value="50" />
<audioApply value="1.000000" />
<weight value="5" />
<type>VMT_GEARBOX</type>
</Item>
<Item>
<identifier />
<modifier value="100" />
<audioApply value="1.000000" />
<weight value="5" />
<type>VMT_GEARBOX</type>
</Item>
<Item>
<identifier />
<modifier value="20" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_ARMOUR</type>
</Item>
<Item>
<identifier />
<modifier value="40" />
<audioApply value="1.000000" />
<weight value="10" />
<type>VMT_ARMOUR</type>
</Item>
<Item>
<identifier />
<modifier value="60" />
<audioApply value="1.000000" />
<weight value="20" />
<type>VMT_ARMOUR</type>
</Item>
<Item>
<identifier />
<modifier value="80" />
<audioApply value="1.000000" />
<weight value="30" />
<type>VMT_ARMOUR</type>
</Item>
<Item>
<identifier />
<modifier value="100" />
<audioApply value="1.000000" />
<weight value="40" />
<type>VMT_ARMOUR</type>
</Item>
<Item>
<identifier>HORN_TRUCK</identifier>
<modifier value="1766676233" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>HORN_COP</identifier>
<modifier value="2904189469" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>HORN_CLOWN</identifier>
<modifier value="2543206147" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>HORN_MUSICAL_1</identifier>
<modifier value="1732399718" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>HORN_MUSICAL_2</identifier>
<modifier value="2046162893" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>HORN_MUSICAL_3</identifier>
<modifier value="2194999691" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>HORN_MUSICAL_4</identifier>
<modifier value="2508304100" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>HORN_MUSICAL_5</identifier>
<modifier value="3707223535" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<Item>
<identifier>HORN_SAD_TROMBONE</identifier>
<modifier value="632950117" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>MUSICAL_HORN_BUSINESS_1</identifier>
<modifier value="3628534289" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>MUSICAL_HORN_BUSINESS_2</identifier>
<modifier value="3892554122" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>MUSICAL_HORN_BUSINESS_3</identifier>
<modifier value="4112892878" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>MUSICAL_HORN_BUSINESS_4</identifier>
<modifier value="116877169" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>MUSICAL_HORN_BUSINESS_5</identifier>
<modifier value="2684983719" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>MUSICAL_HORN_BUSINESS_6</identifier>
<modifier value="2982690084" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
		</Item>
		<Item>
<identifier>MUSICAL_HORN_BUSINESS_7</identifier>
<modifier value="3203290992" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
		</Item>
		<Item>
<identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
<modifier value="771284519" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
		</Item>
		<Item>
<identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
<modifier value="2586621229" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
		</Item>
		<Item>
<identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
<modifier value="283386134" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
		</Item>
		<Item>
<identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
<modifier value="3884502400" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
		</Item>
		<Item>
<identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
<modifier value="265723083" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
		</Item>
		<Item>
<identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
<modifier value="1746883687" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
		</Item>
		<Item>
<identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
<modifier value="1919870950" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
		</Item>
		<Item>
<identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
<modifier value="1085277077" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
		</Item>
<Item>
<identifier>HIPSTER_HORN_1</identifier>
<modifier value="444549672" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>HIPSTER_HORN_2</identifier>
<modifier value="1603064898" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>HIPSTER_HORN_3</identifier>
<modifier value="240366033" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>HIPSTER_HORN_4</identifier>
<modifier value="960137118" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>INDEP_HORN_1</identifier>
<modifier value="3572144790" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>INDEP_HORN_2</identifier>
<modifier value="3801396714" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>INDEP_HORN_3</identifier>
<modifier value="2843657151" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
<Item>
<identifier>INDEP_HORN_4</identifier>
<modifier value="3341811489" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<Item>
<identifier>LUXE_HORN_1</identifier>
<modifier value="3199657341" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<Item>
<identifier>LUXE_HORN_2</identifier>
<modifier value="2900378064" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<Item>
<identifier>LUXE_HORN_3</identifier>
<modifier value="3956195248" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
<identifier>LUXORY_HORN_1</identifier>
<modifier value="676333254" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
<identifier>LUXURY_HORN_2</identifier>
<modifier value="2099578296" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
<identifier>LUXURY_HORN_3</identifier>
<modifier value="1373384483" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<Item>
<identifier>ORGAN_HORN_LOOP_01</identifier>
<modifier value="2916775806" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
<identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
<modifier value="3714706952" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<Item>
<identifier>ORGAN_HORN_LOOP_02</identifier>
<modifier value="2611860261" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
<identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
<modifier value="3206770359" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<Item>
<identifier>LOWRIDER_HORN_1</identifier>
<modifier value="310529291" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
<identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
<modifier value="2965568987" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<Item>
<identifier>LOWRIDER_HORN_2</identifier>
<modifier value="55291550" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
<identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
<modifier value="965054819" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_HORN</type>
</Item>
		<Item>
<identifier />
<modifier value="1" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_SUSPENSION</type>
</Item>
<Item>
<identifier />
<modifier value="2" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_SUSPENSION</type>
</Item>
<Item>
<identifier />
<modifier value="3" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_SUSPENSION</type>
</Item>
<Item>
<identifier />
<modifier value="4" />
<audioApply value="1.000000" />
<weight value="0" />
<type>VMT_SUSPENSION</type>
</Item>
</statMods>
<slotNames />
<liveryNames>
</liveryNames>
</Item>
</Kits>
<Lights>
<Item>
<id value="8564" />
<indicator>
<intensity value="0.375000" />
<falloffMax value="2.500000" />
<falloffExponent value="8.000000" />
<innerConeAngle value="20.000000" />
<outerConeAngle value="50.000000" />
<emmissiveBoost value="false" />
<color value="0xFFFF7300" />
</indicator>
<rearIndicatorCorona>
<size value="0.000000" />
<size_far value="0.000000" />
<intensity value="0.000000" />
<intensity_far value="0.000000" />
<color value="0x00000000" />
<numCoronas value="0" />
<distBetweenCoronas value="128" />
<distBetweenCoronas_far value="255" />
<xRotation value="0.000000" />
<yRotation value="0.000000" />
<zRotation value="0.000000" />
<zBias value="0.250000" />
<pullCoronaIn value="false" />
</rearIndicatorCorona>
<frontIndicatorCorona>
<size value="0.000000" />
<size_far value="0.000000" />
<intensity value="0.000000" />
<intensity_far value="0.000000" />
<color value="0x00000000" />
<numCoronas value="1" />
<distBetweenCoronas value="128" />
<distBetweenCoronas_far value="255" />
<xRotation value="0.000000" />
<yRotation value="0.000000" />
<zRotation value="0.000000" />
<zBias value="0.250000" />
<pullCoronaIn value="false" />
</frontIndicatorCorona>
<tailLight>
<intensity value="0.250000" />
<falloffMax value="4.000000" />
<falloffExponent value="16.000000" />
<innerConeAngle value="45.000000" />
<outerConeAngle value="90.000000" />
<emmissiveBoost value="false" />
<color value="0xFFFF0000" />
</tailLight>
<tailLightCorona>
<size value="1.200000" />
<size_far value="2.500000" />
<intensity value="5.000000" />
<intensity_far value="1.000000" />
<color value="0xFFFF0F05" />
<numCoronas value="0" />
<distBetweenCoronas value="25" />
<distBetweenCoronas_far value="49" />
<xRotation value="0.270177" />
<yRotation value="1.407433" />
<zRotation value="0.000000" />
<zBias value="0.250000" />
<pullCoronaIn value="false" />
</tailLightCorona>
<tailLightMiddleCorona>
<size value="0.000000" />
<size_far value="0.000000" />
<intensity value="0.000000" />
<intensity_far value="0.000000" />
<color value="0x00000000" />
<numCoronas value="0" />
<distBetweenCoronas value="128" />
<distBetweenCoronas_far value="255" />
<xRotation value="0.000000" />
<yRotation value="0.000000" />
<zRotation value="0.000000" />
<zBias value="0.250000" />
<pullCoronaIn value="false" />
</tailLightMiddleCorona>
<headLight>
<intensity value="1.500000" />
<falloffMax value="35.000000" />
<falloffExponent value="16.000000" />
<innerConeAngle value="0.000000" />
<outerConeAngle value="60.000000" />
<emmissiveBoost value="false" />
<color value="0xFF7FA7E3" />
<textureName>VehicleLight_car_standardmodern</textureName>
<mirrorTexture value="false" />
</headLight>
<headLightCorona>
<size value="0.100000" />
<size_far value="7.000000" />
<intensity value="7.000000" />
<intensity_far value="5.000000" />
<color value="0xFF61A5FF" />
<numCoronas value="0" />
<distBetweenCoronas value="27" />
<distBetweenCoronas_far value="89" />
<xRotation value="0.000000" />
<yRotation value="0.175929" />
<zRotation value="0.527788" />
<zBias value="0.250000" />
<pullCoronaIn value="false" />
</headLightCorona>
<reversingLight>
<intensity value="0.500000" />
<falloffMax value="7.200000" />
<falloffExponent value="32.000000" />
<innerConeAngle value="20.000000" />
<outerConeAngle value="78.000000" />
<emmissiveBoost value="false" />
<color value="0xFFFFFFFF" />
</reversingLight>
<reversingLightCorona>
<size value="0.800000" />
<size_far value="2.000000" />
<intensity value="1.500000" />
<intensity_far value="1.000000" />
<color value="0x00F7F7F7" />
<numCoronas value="0" />
<distBetweenCoronas value="128" />
<distBetweenCoronas_far value="255" />
<xRotation value="0.000000" />
<yRotation value="0.000000" />
<zRotation value="0.000000" />
<zBias value="0.250000" />
<pullCoronaIn value="false" />
</reversingLightCorona>
<name>rmodjeepg</name>
</Item>
</Lights>






    <Sirens>
      <Item>
        <id value="623457453"/> 
        <name>aa</name>
        <timeMultiplier value="1.50000000"/>
        <lightFalloffMax value="125.00000000"/>
        <lightFalloffExponent value="125.00000000"/>
        <lightInnerConeAngle value="2.29061000"/>
        <lightOuterConeAngle value="70.00000000"/>
        <lightOffset value="0.00000000"/>
        <textureName>VehicleLight_sirenlight</textureName>
        <sequencerBpm value="200"/>
        <leftHeadLight>
        <sequencer value="0"/> <!--557910036 -->
        </leftHeadLight>
        <rightHeadLight>
        <sequencer value="0"/> <!-- 134758533 -->
        </rightHeadLight>
        <leftTailLight>
        <sequencer value="0"/>
        </leftTailLight>
        <rightTailLight>
        <sequencer value="0"/>
        </rightTailLight>
        <leftHeadLightMultiples value="1"/>
        <rightHeadLightMultiples value="1"/>
        <leftTailLightMultiples value="1"/>
        <rightTailLightMultiples value="1"/>
        <useRealLights value="false"/>
        <sirens>
      <!---boy-siren1-->
      <Item> 
        <rotation>
        <delta value="0.00000000"/>
        <start value="0.00000000"/>
        <speed value="0.00000000"/>
        <sequencer value="1073479680"/>
        <multiples value="1"/>
        <direction value="false"/>
        <syncToBpm value="true"/>
        </rotation>
        <flashiness>
        <delta value="0.00000000"/>
        <start value="0.00000000"/>
        <speed value="0.00000000"/>
        <sequencer value="1073479680"/>
        <multiples value="2"/>
        <direction value="true"/>
        <syncToBpm value="true"/>
        </flashiness>
        <corona>
        <intensity value="0.00000000"/>
        <size value="0.00000000"/>
        <pull value="0.02000000"/>
        <faceCamera value="false"/>
        </corona>
        <color value="0xFF0000FF"/>
        <intensity value="0.00000000"/>
        <lightGroup value="0"/>
        <rotate value="false"/>
        <scale value="true"/>
        <scaleFactor value="100"/>
        <flash value="true"/>
        <light value="true"/>
        <spotLight value="true"/>
        <castShadows value="true"/>
      </Item>
      <Item> <!--Siren 2-->
        <rotation>
        <delta value="0.00000000"/>
        <start value="0.00000000"/>
        <speed value="0.00000000"/>
        <sequencer value="16376"/>
        <multiples value="1"/>
        <direction value="false"/>
        <syncToBpm value="true"/>
        </rotation>
        <flashiness>
        <delta value="0.00000000"/>
        <start value="0.00000000"/>
        <speed value="0.00000000"/>
        <sequencer value="16376"/>
        <multiples value="2"/>
        <direction value="true"/>
        <syncToBpm value="true"/>
        </flashiness>
        <corona>
        <intensity value="0.00000000"/>
        <size value="0.00000000"/>
        <pull value="0.02000000"/>
        <faceCamera value="false"/>
        </corona>
        <color value="0xFF0000FF"/>
        <intensity value="0.00000000"/>
        <lightGroup value="0"/>
        <rotate value="false"/>
        <scale value="true"/>
        <scaleFactor value="100"/>
        <flash value="true"/>
        <light value="true"/>
        <spotLight value="true"/>
        <castShadows value="true"/>
      </Item>  





      <!---boy-siren3-->
      <Item> 
        <rotation>
        <delta value="0.00000000"/>
        <start value="0.00000000"/>
        <speed value="0.00000000"/>
        <sequencer value="1073479680"/>
        <multiples value="1"/>
        <direction value="false"/>
        <syncToBpm value="true"/>
        </rotation>
        <flashiness>
        <delta value="0.00000000"/>
        <start value="0.00000000"/>
        <speed value="0.00000000"/>
        <sequencer value="1073479680"/>
        <multiples value="2"/>
        <direction value="true"/>
        <syncToBpm value="true"/>
        </flashiness>
        <corona>
        <intensity value="0.00000000"/>
        <size value="0.00000000"/>
        <pull value="0.02000000"/>
        <faceCamera value="false"/>
        </corona>
        <color value="0xFF0000FF"/>
        <intensity value="0.00000000"/>
        <lightGroup value="0"/>
        <rotate value="false"/>
        <scale value="true"/>
        <scaleFactor value="100"/>
        <flash value="true"/>
        <light value="true"/>
        <spotLight value="true"/>
        <castShadows value="true"/>
      </Item>
      <Item> <!--Siren 4-->
        <rotation>
        <delta value="0.00000000"/>
        <start value="0.00000000"/>
        <speed value="0.00000000"/>
        <sequencer value="16376"/>
        <multiples value="1"/>
        <direction value="false"/>
        <syncToBpm value="true"/>
        </rotation>
        <flashiness>
        <delta value="0.00000000"/>
        <start value="0.00000000"/>
        <speed value="0.00000000"/>
        <sequencer value="16376"/>
        <multiples value="2"/>
        <direction value="true"/>
        <syncToBpm value="true"/>
        </flashiness>
        <corona>
        <intensity value="0.00000000"/>
        <size value="0.00000000"/>
        <pull value="0.02000000"/>
        <faceCamera value="false"/>
        </corona>
        <color value="0xFF0000FF"/>
        <intensity value="0.00000000"/>
        <lightGroup value="0"/>
        <rotate value="false"/>
        <scale value="true"/>
        <scaleFactor value="100"/>
        <flash value="true"/>
        <light value="true"/>
        <spotLight value="true"/>
        <castShadows value="true"/>
      </Item>  





      </sirens>
    </Item>
    </Sirens>




















</CVehicleModelInfoVarGlobal>