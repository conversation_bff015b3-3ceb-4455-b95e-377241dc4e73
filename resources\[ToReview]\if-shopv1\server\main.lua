local FrameworkSelected = Config.Framework and Config.Framework:lower() or nil

local ESX = nil
local QBCore = nil

if (FrameworkSelected == "esx") then
    if (Config.FrameworkCore) then
        ESX = exports[Config.FrameworkCore]:getSharedObject()
        if (not ESX) then
            TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        end
    else
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
    end
    if (not ESX) then
        print('     ERROR: Invalid FrameworkCore! Pls fix it on Config!!!')
    end
elseif (FrameworkSelected == "qb" or FrameworkSelected == "qb-core" or FrameworkSelected == "qbcore") then
    if (Config.FrameworkCore) then
        QBCore = exports[Config.FrameworkCore]:GetCoreObject()
    else
        print('     ERROR: Invalid FrameworkCore! Pls fix it on Config!!!')
    end
else
    print('     ERROR: Unknown Framework! Available: ESX or QB!')
end

local function getFrameworkPlayerBalance(FPlayer)
	local totalBalance = 0

	if not FPlayer then
		return 0
	end

	if (ESX) then
		for _, payMethod in ipairs(Config.PayMethod) do
			local method = payMethod
			if (method == 'cash') then method = 'money' end

			local account = FPlayer.getAccount(method)
			if account then
				local money = account.money or 0
				totalBalance = totalBalance + money
			end
		end
	elseif (QBCore) then
		for _, payMethod in ipairs(Config.PayMethod) do
			local method = payMethod
			if (method == 'money') then method = 'cash' end

			local money = FPlayer.Functions.GetMoney(method) or 0
			totalBalance = totalBalance + money
		end
	end

	return tonumber(totalBalance)
end
local function removeFrameworkPlayerMoney(FPlayer, amount)
	-- Try to pay with preferred methods in order
	local remainingAmount = amount

	if not FPlayer then
		return false
	end

	if (ESX) then
		for _, payMethod in ipairs(Config.PayMethod) do
			if remainingAmount <= 0 then break end

			local method = payMethod
			if (method == 'cash') then method = 'money' end

			local account = FPlayer.getAccount(method)
			if not account then
				goto continue_esx
			end

			local accountMoney = account.money or 0
			local amountToRemove = math.min(accountMoney, remainingAmount)

			if amountToRemove > 0 then
				FPlayer.removeAccountMoney(method, amountToRemove)
				remainingAmount = remainingAmount - amountToRemove
			end
			::continue_esx::
		end
	elseif (QBCore) then
		for _, payMethod in ipairs(Config.PayMethod) do
			if remainingAmount <= 0 then break end

			local method = payMethod
			if (method == 'money') then method = 'cash' end

			local accountMoney = FPlayer.Functions.GetMoney(method) or 0
			if accountMoney == nil then
				goto continue_qb
			end

			local amountToRemove = math.min(accountMoney, remainingAmount)

			if amountToRemove > 0 then
				FPlayer.Functions.RemoveMoney(method, amountToRemove)
				remainingAmount = remainingAmount - amountToRemove
			end
			::continue_qb::
		end
	end

	return remainingAmount <= 0
end

local function canAfford(source, cb, totalPrice, shoppingCart)
	local _source = source
	local FPlayer = ESX and ESX.GetPlayerFromId(_source) or QBCore and QBCore.Functions.GetPlayer(_source)

	if not FPlayer then
		cb(false)
		return
	end

	local playerBalance = getFrameworkPlayerBalance(FPlayer)
	totalPrice = tonumber(totalPrice)

	if (playerBalance and playerBalance >= totalPrice) then
		-- First remove money
		local paymentSuccess = removeFrameworkPlayerMoney(FPlayer, totalPrice)

		if not paymentSuccess then
			cb(false)
			return
		end

		-- Then add items
		for k, v in pairs(shoppingCart) do
			local amount = v.amount or 1

			if (ESX) then
				-- ESX can use different methods depending on version
				if FPlayer.addInventoryItem then
					FPlayer.addInventoryItem(k, amount)
				elseif FPlayer.addItem then
					FPlayer.addItem(k, amount)
				else
					print("ERROR: Could not find method to add item in ESX")
				end
			elseif (QBCore) then
				FPlayer.Functions.AddItem(k, amount)
			end
		end

		cb(true)
	else
		cb(false)
	end
end

if (ESX) then ESX.RegisterServerCallback('if-shopv1:canAfford', canAfford)
elseif (QBCore) then QBCore.Functions.CreateCallback('if-shopv1:canAfford', canAfford)
end