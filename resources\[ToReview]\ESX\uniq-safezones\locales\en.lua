Locales['en'] = {
    --- # Commands # ---
    ['command.creator.description'] = 'Access the Safe Zone Creator menu',
    --- ! # Commands # ---

    --- # Menu # ---
    -- Menu
    ['menu.admin_menu'] = 'Safe Zone Creator',
    ['menu.create_safezone'] = 'Create Safe Zone',
    ['menu.delete_safezone'] = 'Delete Safe Zone',
    ['menu.safezones_list'] = 'Safe Zones List',
    ['menu.refresh_safezones'] = 'Refresh Safe Zones',

    -- Input
    ['input.safezone_label'] = 'Label',
    ['input.safezone_label.description'] = 'Label for this Safe Zone, will be displayed in safezones list menu',
    ['input.safezone_label.placeholder'] = 'LSPD Parking Lot',
    ['input.safezone_points'] = 'Points',
    ['input.safezone_points.description'] = 'Safe Zone points with which the safe zone will be marked (if it\'s not clear to you, check debug mode and create a test safe zone)',
    ['input.text_ui'] = 'Text UI (help text)',
    ['input.allow_driveby'] = 'Allow Driveby',
    ['input.allow_weapons'] = 'Allow Weapons',
    ['input.max_veh_speed'] = 'Max Vehicle Speed (KM/H)',
    ['input.max_veh_speed.description'] = 'Maximum vehicle speed allowed inside the safezone (optional)',
    ['input.players_invisible'] = 'Players Invisible',
    ['input.vehicles_invisible'] = 'Vehicles Invisible',
    ['input.delete_safezone'] = 'Delete Safe Zone',
    ['input.select_option'] = 'Select Option',
    ['input.input_radius'] = 'Input Radius',
    ['input.input_id'] = 'Input Safe Zone database ID to delete',
    ['input.delete_id'] = 'Delete By Safe Zone ID',
    ['input.delete_closest'] = 'Delete Closest Safe Zone In %sm Radius',
    ['input.delete_radius'] = 'Delete All Safe Zones In Radius',
    ['input.specific_jobs'] = 'Specific Jobs',
    ['input.specific_jobs_desc'] = 'Jobs that are allowed to use weapons in the safe zone (empty - none)',

    -- Context
    ['context.safezone_id'] = 'Safe Zone #%s',
    ['context.safezone_name'] = 'Label: %s',
    ['context.edit_name'] = 'Edit Safe Zone Label',
    ['context.current_name'] = 'Current Label',
    ['context.teleport'] = 'Teleport To Safe Zone',
    ['context.teleport.description'] = 'This will teleport you to Safe Zone #%s',
    ['context.toggle_debug'] = 'Toggle Debug Mode',
    ['context.toggle_debug.status'] = 'Current Status: %s',
    ['context.toggle_debug.status.enabled'] = 'enabled',
    ['context.toggle_debug.status.disabled'] = 'disabled',
    ['context.edit_points'] = 'Edit Points',
    ['context.edit_points.description'] = 'Remove or change location of safe zone points',
    ['context.points_list'] = 'Safe Zone Points',
    ['context.point_id'] = 'Point #%s',
    ['context.teleport_point'] = 'Teleport To Point',
    ['context.delete_point'] = 'Delete Point',
    ['context.delete_point.description'] = 'This will permanently delete Point #%s for Safe Zone #%s',
    ['context.edit_point_coords'] = 'Edit Point Coordinates',
    ['context.edit_point_coords.description'] = 'Move point permanently to different location',
    ['context.add_points'] = 'Add Points',
    ['context.add_points.description'] = 'Add points for this safe zone, you can later edit or delete them',
    ['context.delete_safezone.description'] = 'This will delete this Safe Zone permanently. All data will be lost',
    ['context.safezone_settings'] = 'Safe Zone Settings',
    ['context.safezone_settings.description'] = 'Configure this Safe Zone. It is possible to configure all the settings configurable in safe zone creator',
    ['context.specific_jobs_desc'] = 'Manage specific jobs - jobs that are allowed to use weapons in the safe zone',

    --- ! # Menu # ---

    --- # Notify # ---
    ['notify.success_cancel'] = 'You have cancelled creating Safe Zone',
    ['notify.success_cancel_points'] = 'You have cancelled creating points',
    ['notify.point_id_created'] = 'Point #%s placed',
    ['notify.all_points_created'] = 'All points placed',
    ['notify.safezone_created'] = 'Safe Zone "%s" successfully created',
    ['notify.no_safezones_in_radius'] = 'There are no Safe Zones in radius %sm',
    ['notify.safezone_deleted'] = 'Safe Zone #%s is successfully deleted',
    ['notify.safezone_not_exist'] = 'Safe Zone #%s does not exist',
    ['notify.name_edited'] = 'Successfully changed name for Safe Zone #%s to "%s" ',
    ['notify.teleported'] = 'Successfully teleported to Safe Zone #%s - "%s"',
    ['notify.debug_mode'] = 'Debug Mode is now', -- enabled/disabled (context.toggle_debug.status)
    ['notify.canceled_updating'] = 'You have cancelled updating safe zone point',
    ['notify.safezone_configured'] = 'Safe Zone #%s is successfully configured',
    ['notify.points_updated'] = 'Points for Safe Zone #%s successfully updated',
    ['notify.point_deleted'] = 'Point #%s in Safe Zone #%s successfully deleted',
    ['notify.point_updated'] = 'Coords for point #%s in Safe Zone #%s successfully updated',
    ['notify.nothing_configurable'] = 'Nothing can be configured here (Contact Developer)',
    ['notify.specific_jobs_updated'] = 'Specific jobs access has been updated',
    --- ! # Notify # ---
    
    --- # Text UI # ---
    ['textui.creating_point'] = {
        ('Each point must be the same height as the others (same z coordinate)  \n'),
        ('[ENTER] - Set point #%s  \n'),
        ('[BACKSPACE] - Cancel creating Safe Zone  \n'),
    },
    ['textui.creating_points'] = {
        ('Each point must be the same height as the others (same z coordinate)  \n'),
        ('[ENTER] - Set point #%s  \n'),
        ('[G] - Finish creating points  \n'),
        ('[BACKSPACE] - Cancel creating points (everything will be dismissed)  \n'),
    },
    ['textui.editing_point'] = {
        ('[ENTER] - Update point #%s location  \n'),
        ('[BACKSPACE] - Cancel updating point  \n')
    },
    --- ! # Text UI # ---
    
    --- # Alert Dialog # ---
    ['alert.cancel_creating'] = 'Cancel Creating Safe Zone',
    ['alert.cancel_creating.sure'] = 'Are you sure you want to cancel creating this safe zone? The progress will not be saved.',
    ['alert.delete_point'] = 'Delete Point',
    ['alert.delete_point.sure'] = 'Are you sure you want to delete this point? The point will be deleted permanently.',
    ['alert.cancel_creating_points'] = 'Cancel Creating Points',
    ['alert.cancel_creating_points.sure'] = 'Are you sure you want to cancel creating points for this safe zone? The progress will not be saved.',
    ['alert.delete_safezone'] = 'Delete Safe Zone',
    ['alert.delete_safezone.sure'] = 'Are you sure you want to delete safe zone? The data will be lost permanently.',
    --- ! # Alert Dialog # ----

    --- # Logs # ---
    ['logs.safezone_created'] = '%s just created a new Safe Zone.\nLabel: **%s**\nData: **%s**',
    ['logs.safezone_deleted'] = '%s just deleted a Safe Zone #%s',
    ['logs.safezone_label_edited'] = '%s just edited a Safe Zone #%s label.\nNew Label: **%s**',
    ['logs.debug_mode_switch'] = '%s just switched Safe Zone #%s debug mode.\nDebug mode is now',
    ['logs.point_updated'] = '%s just edited point #%s for Safe Zone #%s',
    --- ! # Logs # ---
}