ALTER TABLE `users`
	ADD `pregnant` TINYINT(1) NULL DEFAULT '0'
;

INSERT INTO `items` (`name`, `label`, `limit`) VALUES
	('condom', 'CONDONES', 100),
	('levonorgestrel', 'Levonorgestrel', 100),
	('mifepristone', 'Mife<PERSON>ristone', 100),
	('testpack', 'PRUEBA DE EMBARAZO', 100),
	('viagra', 'Viagra', 100)
;

CREATE TABLE IF NOT EXISTS `last_pregnant` (
    `identifier` VARCHAR(255),
    `pregnant_time` INT(255),
    PRIMARY KEY (`identifier`)
);
